"""
Management command to clean up user presence data

This command marks users as offline if they haven't been active for a specified time.
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta
from communications.models import UserPresence


class Command(BaseCommand):
    help = 'Clean up user presence data by marking inactive users as offline'

    def add_arguments(self, parser):
        parser.add_argument(
            '--minutes',
            type=int,
            default=5,
            help='Mark users offline if inactive for this many minutes (default: 5)'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be updated without making changes'
        )

    def handle(self, *args, **options):
        minutes = options['minutes']
        dry_run = options['dry_run']
        
        cutoff_time = timezone.now() - timedelta(minutes=minutes)
        
        # Find users who should be marked offline
        stale_presences = UserPresence.objects.filter(
            status__in=['online', 'away'],
            last_activity__lt=cutoff_time
        )
        
        count = stale_presences.count()
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING(
                    f'DRY RUN: Would mark {count} users as offline '
                    f'(inactive for more than {minutes} minutes)'
                )
            )
            for presence in stale_presences:
                self.stdout.write(
                    f'  - {presence.user.name} (last active: {presence.last_activity})'
                )
        else:
            # Update the presences
            updated = stale_presences.update(
                status='offline',
                last_seen=timezone.now()
            )
            
            self.stdout.write(
                self.style.SUCCESS(
                    f'Successfully marked {updated} users as offline '
                    f'(inactive for more than {minutes} minutes)'
                )
            )
