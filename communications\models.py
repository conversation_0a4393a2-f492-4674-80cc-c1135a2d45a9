"""
Communications Models for HeartGrid Dating Platform

This module contains models for real-time messaging, user presence tracking,
and communication-related functionality.
"""

import uuid
from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.core.exceptions import ValidationError

User = get_user_model()


class UserPresence(models.Model):
    """
    Tracks user online/offline status and last seen information
    """
    STATUS_CHOICES = [
        ('online', 'Online'),
        ('away', 'Away'),
        ('offline', 'Offline'),
    ]

    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='presence')
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default='offline')
    last_seen = models.DateTimeField(auto_now=True)
    last_activity = models.DateTimeField(auto_now=True)
    is_typing_to = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='typing_indicators')

    # Call availability status
    CALL_AVAILABILITY = [
        ('available', 'Available for calls'),
        ('busy', 'Busy - Do not disturb'),
        ('in_call', 'Currently in a call'),
    ]

    call_availability = models.CharField(
        max_length=15,
        choices=CALL_AVAILABILITY,
        default='available'
    )
    current_call_session = models.ForeignKey(
        'CallSession',
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name='active_presence'
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'communications_user_presence'

    def __str__(self):
        return f"{self.user.name} - {self.status}"

    def set_online(self):
        """Set user status to online"""
        self.status = 'online'
        self.last_activity = timezone.now()
        self.save()

    def set_offline(self):
        """Set user status to offline"""
        self.status = 'offline'
        self.last_seen = timezone.now()
        self.save()

    def set_typing(self, to_user):
        """Set typing indicator for a specific user"""
        self.is_typing_to = to_user
        self.save()

    def clear_typing(self):
        """Clear typing indicator"""
        self.is_typing_to = None
        self.save()

    def set_in_call(self, call_session):
        """Set user as currently in a call"""
        self.call_availability = 'in_call'
        self.current_call_session = call_session
        self.save(update_fields=['call_availability', 'current_call_session'])

    def set_available_for_calls(self):
        """Set user as available for calls"""
        self.call_availability = 'available'
        self.current_call_session = None
        self.save(update_fields=['call_availability', 'current_call_session'])

    def set_busy(self):
        """Set user as busy (do not disturb)"""
        self.call_availability = 'busy'
        self.save(update_fields=['call_availability'])

    def can_receive_calls(self):
        """Check if user can receive calls"""
        return (
            self.status == 'online' and
            self.call_availability == 'available' and
            not self.current_call_session
        )


class Conversation(models.Model):
    """
    Represents a conversation between two users
    """
    CONVERSATION_TYPES = [
        ('direct', 'Direct Message'),
        ('match', 'Match Conversation'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    participants = models.ManyToManyField(User, related_name='conversations')
    conversation_type = models.CharField(max_length=10, choices=CONVERSATION_TYPES, default='direct')

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    last_message_at = models.DateTimeField(null=True, blank=True)

    # Conversation settings
    is_active = models.BooleanField(default=True)
    is_archived = models.BooleanField(default=False)

    class Meta:
        db_table = 'communications_conversations'
        ordering = ['-last_message_at', '-updated_at']

    def __str__(self):
        participant_names = ", ".join([user.name for user in self.participants.all()[:2]])
        return f"Conversation: {participant_names}"

    def get_other_participant(self, user):
        """Get the other participant in a direct conversation"""
        return self.participants.exclude(id=user.id).first()

    def update_last_message_time(self):
        """Update the last message timestamp"""
        self.last_message_at = timezone.now()
        self.save(update_fields=['last_message_at'])


class Message(models.Model):
    """
    Individual messages within conversations
    """
    MESSAGE_TYPES = [
        ('text', 'Text Message'),
        ('image', 'Image'),
        ('file', 'File'),
        ('system', 'System Message'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    conversation = models.ForeignKey(Conversation, on_delete=models.CASCADE, related_name='messages')
    sender = models.ForeignKey(User, on_delete=models.CASCADE, related_name='communications_sent_messages')

    # Message content
    message_type = models.CharField(max_length=10, choices=MESSAGE_TYPES, default='text')
    content = models.TextField(blank=True)
    file_attachment = models.FileField(upload_to='messages/attachments/', null=True, blank=True)

    # Message metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    edited_at = models.DateTimeField(null=True, blank=True)

    # Message status
    is_deleted = models.BooleanField(default=False)
    deleted_at = models.DateTimeField(null=True, blank=True)

    # Reactions (emoji reactions to messages)
    reactions = models.JSONField(default=dict, blank=True)  # {user_id: emoji}

    class Meta:
        db_table = 'communications_messages'
        ordering = ['created_at']

    def __str__(self):
        return f"Message from {self.sender.name} in {self.conversation}"

    def save(self, *args, **kwargs):
        is_new = self.pk is None
        super().save(*args, **kwargs)

        # Update conversation's last message time
        if is_new:
            self.conversation.update_last_message_time()

    def soft_delete(self):
        """Soft delete the message"""
        self.is_deleted = True
        self.deleted_at = timezone.now()
        self.save()

    def add_reaction(self, user, emoji):
        """Add or update a reaction to the message"""
        self.reactions[str(user.id)] = emoji
        self.save()

    def remove_reaction(self, user):
        """Remove a user's reaction from the message"""
        self.reactions.pop(str(user.id), None)
        self.save()


class MessageStatus(models.Model):
    """
    Tracks read/unread status of messages for each user
    """
    STATUS_CHOICES = [
        ('sent', 'Sent'),
        ('delivered', 'Delivered'),
        ('read', 'Read'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    message = models.ForeignKey(Message, on_delete=models.CASCADE, related_name='statuses')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='message_statuses')
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default='sent')

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    read_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        db_table = 'communications_message_status'
        unique_together = ['message', 'user']

    def __str__(self):
        return f"{self.user.name} - {self.message} - {self.status}"

    def mark_as_read(self):
        """Mark message as read"""
        self.status = 'read'
        self.read_at = timezone.now()
        self.save()

    def mark_as_delivered(self):
        """Mark message as delivered"""
        if self.status == 'sent':
            self.status = 'delivered'
            self.save()


class ConversationParticipant(models.Model):
    """
    Tracks participant-specific settings for conversations
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    conversation = models.ForeignKey(Conversation, on_delete=models.CASCADE, related_name='participant_settings')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='conversation_settings')

    # Participant settings
    is_muted = models.BooleanField(default=False)
    is_archived = models.BooleanField(default=False)
    last_read_message = models.ForeignKey(Message, on_delete=models.SET_NULL, null=True, blank=True)
    last_read_at = models.DateTimeField(null=True, blank=True)

    # Notification settings
    notifications_enabled = models.BooleanField(default=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'communications_conversation_participants'
        unique_together = ['conversation', 'user']

    def __str__(self):
        return f"{self.user.name} in {self.conversation}"

    def mark_as_read(self, message=None):
        """Mark conversation as read up to a specific message"""
        if message is None:
            # Mark as read up to the latest message
            message = self.conversation.messages.filter(is_deleted=False).last()

        if message:
            self.last_read_message = message
            self.last_read_at = timezone.now()
            self.save()

    def get_unread_count(self):
        """Get count of unread messages"""
        if not self.last_read_message:
            return self.conversation.messages.filter(is_deleted=False).exclude(sender=self.user).count()

        return self.conversation.messages.filter(
            is_deleted=False,
            created_at__gt=self.last_read_message.created_at
        ).exclude(sender=self.user).count()


class CallSession(models.Model):
    """
    Model to track voice and video call sessions
    """
    CALL_TYPES = [
        ('voice', 'Voice Call'),
        ('video', 'Video Call'),
    ]

    CALL_STATES = [
        ('initiating', 'Initiating'),
        ('ringing', 'Ringing'),
        ('connecting', 'Connecting'),
        ('active', 'Active'),
        ('ended', 'Ended'),
        ('missed', 'Missed'),
        ('rejected', 'Rejected'),
        ('failed', 'Failed'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    conversation = models.ForeignKey(
        Conversation,
        on_delete=models.CASCADE,
        related_name='call_sessions'
    )
    caller = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='initiated_calls'
    )
    callee = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='received_calls'
    )
    call_type = models.CharField(max_length=10, choices=CALL_TYPES)
    state = models.CharField(max_length=15, choices=CALL_STATES, default='initiating')
    started_at = models.DateTimeField(auto_now_add=True)
    answered_at = models.DateTimeField(null=True, blank=True)
    ended_at = models.DateTimeField(null=True, blank=True)
    duration = models.DurationField(null=True, blank=True)
    end_reason = models.CharField(max_length=50, blank=True)

    # WebRTC session data
    caller_ice_candidates = models.JSONField(default=list, blank=True)
    callee_ice_candidates = models.JSONField(default=list, blank=True)
    webrtc_offer = models.JSONField(null=True, blank=True)
    webrtc_answer = models.JSONField(null=True, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'communications_call_sessions'
        ordering = ['-started_at']
        indexes = [
            models.Index(fields=['caller', '-started_at']),
            models.Index(fields=['callee', '-started_at']),
            models.Index(fields=['conversation', '-started_at']),
            models.Index(fields=['state']),
        ]

    def __str__(self):
        return f"{self.get_call_type_display()} call from {self.caller.name} to {self.callee.name}"

    def save(self, *args, **kwargs):
        # Calculate duration when call ends
        if self.state == 'ended' and self.answered_at and self.ended_at:
            self.duration = self.ended_at - self.answered_at
        super().save(*args, **kwargs)

    def mark_as_answered(self):
        """Mark call as answered"""
        self.state = 'active'
        self.answered_at = timezone.now()
        self.save(update_fields=['state', 'answered_at'])

    def mark_as_ended(self, reason='normal'):
        """Mark call as ended"""
        self.state = 'ended'
        self.ended_at = timezone.now()
        self.end_reason = reason
        if self.answered_at:
            self.duration = self.ended_at - self.answered_at
        self.save(update_fields=['state', 'ended_at', 'end_reason', 'duration'])

    def mark_as_missed(self):
        """Mark call as missed"""
        self.state = 'missed'
        self.ended_at = timezone.now()
        self.end_reason = 'missed'
        self.save(update_fields=['state', 'ended_at', 'end_reason'])

    def mark_as_rejected(self):
        """Mark call as rejected"""
        self.state = 'rejected'
        self.ended_at = timezone.now()
        self.end_reason = 'rejected'
        self.save(update_fields=['state', 'ended_at', 'end_reason'])

    def add_ice_candidate(self, candidate, is_caller=True):
        """Add ICE candidate for WebRTC connection"""
        if is_caller:
            self.caller_ice_candidates.append(candidate)
        else:
            self.callee_ice_candidates.append(candidate)
        self.save(update_fields=['caller_ice_candidates', 'callee_ice_candidates'])

    def set_webrtc_offer(self, offer):
        """Set WebRTC offer"""
        self.webrtc_offer = offer
        self.save(update_fields=['webrtc_offer'])

    def set_webrtc_answer(self, answer):
        """Set WebRTC answer"""
        self.webrtc_answer = answer
        self.save(update_fields=['webrtc_answer'])

    @property
    def is_active(self):
        """Check if call is currently active"""
        return self.state in ['ringing', 'connecting', 'active']

    @property
    def duration_seconds(self):
        """Get call duration in seconds"""
        if self.duration:
            return int(self.duration.total_seconds())
        return 0


class UserCallSettings(models.Model):
    """
    User preferences for voice and video calling
    """
    user = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        related_name='call_settings'
    )
    voice_calls_enabled = models.BooleanField(default=True)
    video_calls_enabled = models.BooleanField(default=True)
    auto_reject_calls = models.BooleanField(default=False)
    call_notifications_enabled = models.BooleanField(default=True)

    # Call quality preferences
    preferred_video_quality = models.CharField(
        max_length=10,
        choices=[
            ('low', 'Low (240p)'),
            ('medium', 'Medium (480p)'),
            ('high', 'High (720p)'),
            ('hd', 'HD (1080p)'),
        ],
        default='medium'
    )

    # Privacy settings
    allow_calls_from_strangers = models.BooleanField(default=False)
    show_call_history = models.BooleanField(default=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'communications_user_call_settings'

    def __str__(self):
        return f"Call settings for {self.user.name}"

    def can_receive_voice_calls(self):
        """Check if user can receive voice calls"""
        return self.voice_calls_enabled and not self.auto_reject_calls

    def can_receive_video_calls(self):
        """Check if user can receive video calls"""
        return self.video_calls_enabled and not self.auto_reject_calls
