"""
HeartGrid Django Admin Configuration

This module contains admin interface configurations for the HeartGrid models.
"""

from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.utils.html import format_html
from .models import (
    User, Profile, Photo, Like, Match, Message,
    Subscription, CryptoPayment, AdminUser, Notification,
    UserActivity, Achievement
)


@admin.register(User)
class UserAdmin(BaseUserAdmin):
    """
    Admin interface for User model
    """
    list_display = ['email', 'name', 'age', 'is_active', 'auth_method', 'created_at']
    list_filter = ['is_active', 'auth_method', 'created_at']
    search_fields = ['email', 'name']
    ordering = ['-created_at']

    fieldsets = (
        (None, {'fields': ('email', 'password')}),
        ('Personal info', {'fields': ('name', 'date_of_birth', 'age')}),
        ('Permissions', {'fields': ('is_active', 'is_staff', 'is_superuser', 'groups', 'user_permissions')}),
        ('Important dates', {'fields': ('last_login', 'date_joined')}),
        ('HeartGrid info', {'fields': ('auth_method',)}),
    )

    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': ('email', 'name', 'password1', 'password2'),
        }),
    )


class PhotoInline(admin.TabularInline):
    """
    Inline admin for photos
    """
    model = Photo
    extra = 0
    readonly_fields = ['uploaded_at']


@admin.register(Profile)
class ProfileAdmin(admin.ModelAdmin):
    """
    Admin interface for Profile model
    """
    list_display = ['user', 'gender', 'interested_in', 'location', 'is_complete', 'is_visible']
    list_filter = ['gender', 'interested_in', 'is_complete', 'is_visible', 'created_at']
    search_fields = ['user__name', 'user__email', 'location']
    inlines = [PhotoInline]
    readonly_fields = ['created_at', 'updated_at']


@admin.register(Photo)
class PhotoAdmin(admin.ModelAdmin):
    """
    Admin interface for Photo model
    """
    list_display = ['profile', 'is_primary', 'order', 'uploaded_at']
    list_filter = ['is_primary', 'uploaded_at']
    search_fields = ['profile__user__name']


@admin.register(Like)
class LikeAdmin(admin.ModelAdmin):
    """
    Admin interface for Like model
    """
    list_display = ['liker', 'liked', 'is_super_like', 'created_at']
    list_filter = ['is_super_like', 'created_at']
    search_fields = ['liker__name', 'liked__name']


@admin.register(Match)
class MatchAdmin(admin.ModelAdmin):
    """
    Admin interface for Match model
    """
    list_display = ['user1', 'user2', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['user1__name', 'user2__name']


@admin.register(Message)
class MessageAdmin(admin.ModelAdmin):
    """
    Admin interface for Message model
    """
    list_display = ['sender', 'receiver', 'message_type', 'is_read', 'created_at']
    list_filter = ['message_type', 'is_read', 'created_at']
    search_fields = ['sender__name', 'receiver__name', 'content']
    readonly_fields = ['created_at', 'read_at']


@admin.register(Subscription)
class SubscriptionAdmin(admin.ModelAdmin):
    """
    Admin interface for Subscription model
    """
    list_display = ['user', 'plan', 'status', 'expires_at', 'created_at']
    list_filter = ['plan', 'status', 'created_at']
    search_fields = ['user__name', 'user__email']
    readonly_fields = ['created_at', 'updated_at']


@admin.register(CryptoPayment)
class CryptoPaymentAdmin(admin.ModelAdmin):
    """
    Admin interface for CryptoPayment model
    """
    list_display = ['user', 'subscription_plan', 'chain', 'amount_usd', 'status', 'created_at']
    list_filter = ['chain', 'token_type', 'status', 'created_at']
    search_fields = ['user__name', 'tx_hash', 'payment_address']
    readonly_fields = ['created_at', 'confirmed_at']


@admin.register(Notification)
class NotificationAdmin(admin.ModelAdmin):
    """
    Admin interface for Notification model
    """
    list_display = ['user', 'notification_type', 'title', 'is_read', 'created_at']
    list_filter = ['notification_type', 'is_read', 'created_at']
    search_fields = ['user__name', 'title', 'message']
    readonly_fields = ['created_at', 'read_at']


@admin.register(UserActivity)
class UserActivityAdmin(admin.ModelAdmin):
    """
    Admin interface for UserActivity model
    """
    list_display = ['user', 'activity_type', 'created_at']
    list_filter = ['activity_type', 'created_at']
    search_fields = ['user__name']
    readonly_fields = ['created_at']


@admin.register(Achievement)
class AchievementAdmin(admin.ModelAdmin):
    """
    Admin interface for Achievement model
    """
    list_display = ['user', 'achievement_type', 'title', 'earned_at']
    list_filter = ['achievement_type', 'earned_at']
    search_fields = ['user__name', 'title']
    readonly_fields = ['earned_at']


@admin.register(AdminUser)
class AdminUserAdmin(admin.ModelAdmin):
    """
    Admin interface for AdminUser model
    """
    list_display = ['user', 'created_at']
    search_fields = ['user__name', 'user__email']
    readonly_fields = ['created_at']


# Custom Admin Views for Analytics
class HeartGridAdminSite(admin.AdminSite):
    """
    Custom admin site with enhanced dashboard
    """
    site_header = 'HeartGrid Admin Dashboard'
    site_title = 'HeartGrid Admin'
    index_title = 'Welcome to HeartGrid Administration'

    def index(self, request, extra_context=None):
        """
        Enhanced admin index with analytics
        """
        from django.db.models import Count, Q
        from django.utils import timezone
        from datetime import timedelta

        extra_context = extra_context or {}

        # Calculate key metrics
        today = timezone.now().date()
        week_ago = today - timedelta(days=7)
        month_ago = today - timedelta(days=30)

        # User metrics
        total_users = User.objects.count()
        new_users_week = User.objects.filter(created_at__date__gte=week_ago).count()
        active_users_week = UserActivity.objects.filter(
            created_at__date__gte=week_ago
        ).values('user').distinct().count()

        # Profile metrics
        complete_profiles = Profile.objects.filter(is_complete=True).count()
        profile_completion_rate = (complete_profiles / total_users * 100) if total_users > 0 else 0

        # Matching metrics
        total_likes = Like.objects.count()
        total_matches = Match.objects.filter(is_active=True).count()
        match_rate = (total_matches / (total_likes / 2) * 100) if total_likes > 0 else 0

        # Messaging metrics
        total_messages = Message.objects.count()
        messages_week = Message.objects.filter(created_at__date__gte=week_ago).count()

        # Subscription metrics
        active_subscriptions = Subscription.objects.filter(
            status='active',
            expires_at__gt=timezone.now()
        ).count()
        subscription_rate = (active_subscriptions / total_users * 100) if total_users > 0 else 0

        # Revenue metrics (from crypto payments)
        confirmed_payments = CryptoPayment.objects.filter(status='confirmed')
        total_revenue = sum(payment.amount_usd for payment in confirmed_payments)
        revenue_week = sum(
            payment.amount_usd for payment in confirmed_payments.filter(
                confirmed_at__date__gte=week_ago
            )
        )

        extra_context.update({
            'analytics': {
                'users': {
                    'total': total_users,
                    'new_week': new_users_week,
                    'active_week': active_users_week,
                    'profile_completion_rate': round(profile_completion_rate, 1)
                },
                'engagement': {
                    'total_likes': total_likes,
                    'total_matches': total_matches,
                    'match_rate': round(match_rate, 1),
                    'total_messages': total_messages,
                    'messages_week': messages_week
                },
                'revenue': {
                    'total_revenue': float(total_revenue),
                    'revenue_week': float(revenue_week),
                    'active_subscriptions': active_subscriptions,
                    'subscription_rate': round(subscription_rate, 1)
                }
            }
        })

        return super().index(request, extra_context)


# Create custom admin site instance
admin_site = HeartGridAdminSite(name='heartgrid_admin')

# Register all models with the custom admin site
admin_site.register(User, UserAdmin)
admin_site.register(Profile, ProfileAdmin)
admin_site.register(Photo, PhotoAdmin)
admin_site.register(Like, LikeAdmin)
admin_site.register(Match, MatchAdmin)
admin_site.register(Message, MessageAdmin)
admin_site.register(Subscription, SubscriptionAdmin)
admin_site.register(CryptoPayment, CryptoPaymentAdmin)
admin_site.register(Notification, NotificationAdmin)
admin_site.register(UserActivity, UserActivityAdmin)
admin_site.register(Achievement, AchievementAdmin)
admin_site.register(AdminUser, AdminUserAdmin)
