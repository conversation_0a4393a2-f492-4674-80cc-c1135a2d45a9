"""
Utility functions for HeartGrid Communications

This module contains helper functions for messaging and presence functionality.
"""

from django.utils import timezone
from datetime import timedelta
from django.contrib.auth import get_user_model
from .models import UserPresence, Conversation, Message

User = get_user_model()


def get_user_status(user):
    """
    Get the current status of a user
    
    Args:
        user: User instance
        
    Returns:
        dict: Status information including status, last_seen, and is_online
    """
    try:
        presence = user.presence
        
        # Check if user is truly online (active within last 5 minutes)
        five_minutes_ago = timezone.now() - timedelta(minutes=5)
        is_truly_online = (
            presence.status == 'online' and 
            presence.last_activity and 
            presence.last_activity > five_minutes_ago
        )
        
        return {
            'status': presence.status if is_truly_online else 'offline',
            'last_seen': presence.last_seen,
            'last_activity': presence.last_activity,
            'is_online': is_truly_online,
            'is_typing_to': presence.is_typing_to_id if presence.is_typing_to else None
        }
    except UserPresence.DoesNotExist:
        return {
            'status': 'offline',
            'last_seen': None,
            'last_activity': None,
            'is_online': False,
            'is_typing_to': None
        }


def get_conversation_participants_status(conversation):
    """
    Get status information for all participants in a conversation
    
    Args:
        conversation: Conversation instance
        
    Returns:
        dict: Mapping of user_id to status information
    """
    participants_status = {}
    
    for participant in conversation.participants.all():
        participants_status[str(participant.id)] = get_user_status(participant)
    
    return participants_status


def create_direct_conversation(user1, user2):
    """
    Create or get a direct conversation between two users
    
    Args:
        user1: First user
        user2: Second user
        
    Returns:
        Conversation: The conversation instance
    """
    # Check if conversation already exists
    existing_conversation = Conversation.objects.filter(
        conversation_type='direct',
        participants=user1
    ).filter(
        participants=user2
    ).first()
    
    if existing_conversation:
        return existing_conversation
    
    # Create new conversation
    conversation = Conversation.objects.create(
        conversation_type='direct'
    )
    
    conversation.participants.add(user1, user2)
    
    # Create participant settings
    from .models import ConversationParticipant
    ConversationParticipant.objects.create(
        conversation=conversation,
        user=user1
    )
    ConversationParticipant.objects.create(
        conversation=conversation,
        user=user2
    )
    
    return conversation


def mark_conversation_as_read(conversation, user, message=None):
    """
    Mark all messages in a conversation as read for a user
    
    Args:
        conversation: Conversation instance
        user: User instance
        message: Optional specific message to mark as read up to
    """
    from .models import ConversationParticipant, MessageStatus
    
    # Get or create participant
    participant, created = ConversationParticipant.objects.get_or_create(
        conversation=conversation,
        user=user
    )
    
    # Get the message to mark as read up to
    if message is None:
        message = conversation.messages.filter(is_deleted=False).last()
    
    if message:
        # Update participant's last read message
        participant.mark_as_read(message)
        
        # Update message statuses
        MessageStatus.objects.filter(
            message__conversation=conversation,
            message__created_at__lte=message.created_at,
            user=user,
            status__in=['sent', 'delivered']
        ).update(
            status='read',
            read_at=timezone.now()
        )


def get_unread_conversations_count(user):
    """
    Get the total number of conversations with unread messages for a user
    
    Args:
        user: User instance
        
    Returns:
        int: Number of conversations with unread messages
    """
    from .models import ConversationParticipant
    
    unread_count = 0
    
    participants = ConversationParticipant.objects.filter(
        user=user,
        conversation__is_active=True
    ).select_related('conversation')
    
    for participant in participants:
        if participant.get_unread_count() > 0:
            unread_count += 1
    
    return unread_count


def get_recent_conversations(user, limit=20):
    """
    Get recent conversations for a user, ordered by last message
    
    Args:
        user: User instance
        limit: Maximum number of conversations to return
        
    Returns:
        QuerySet: Recent conversations
    """
    return Conversation.objects.filter(
        participants=user,
        is_active=True
    ).prefetch_related(
        'participants',
        'participant_settings',
        'messages'
    ).order_by('-last_message_at', '-updated_at')[:limit]


def send_message(conversation, sender, content, message_type='text', file_attachment=None):
    """
    Send a message in a conversation
    
    Args:
        conversation: Conversation instance
        sender: User sending the message
        content: Message content
        message_type: Type of message ('text', 'image', 'file')
        file_attachment: Optional file attachment
        
    Returns:
        Message: The created message instance
    """
    from .models import MessageStatus
    
    # Create the message
    message = Message.objects.create(
        conversation=conversation,
        sender=sender,
        content=content,
        message_type=message_type,
        file_attachment=file_attachment
    )
    
    # Update conversation's last message timestamp
    conversation.last_message_at = message.created_at
    conversation.save(update_fields=['last_message_at'])
    
    # Create message status for other participants
    other_participants = conversation.participants.exclude(id=sender.id)
    for participant in other_participants:
        MessageStatus.objects.create(
            message=message,
            user=participant,
            status='sent'
        )
    
    return message


def cleanup_old_typing_indicators():
    """
    Clean up old typing indicators (older than 10 seconds)
    """
    ten_seconds_ago = timezone.now() - timedelta(seconds=10)
    
    UserPresence.objects.filter(
        is_typing_to__isnull=False,
        updated_at__lt=ten_seconds_ago
    ).update(is_typing_to=None)
