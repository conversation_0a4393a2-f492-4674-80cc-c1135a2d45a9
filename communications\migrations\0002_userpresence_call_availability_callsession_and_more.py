# Generated by Django 5.2.3 on 2025-06-27 09:38

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('communications', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='userpresence',
            name='call_availability',
            field=models.CharField(choices=[('available', 'Available for calls'), ('busy', 'Busy - Do not disturb'), ('in_call', 'Currently in a call')], default='available', max_length=15),
        ),
        migrations.CreateModel(
            name='CallSession',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('call_type', models.CharField(choices=[('voice', 'Voice Call'), ('video', 'Video Call')], max_length=10)),
                ('state', models.CharField(choices=[('initiating', 'Initiating'), ('ringing', 'Ringing'), ('connecting', 'Connecting'), ('active', 'Active'), ('ended', 'Ended'), ('missed', 'Missed'), ('rejected', 'Rejected'), ('failed', 'Failed')], default='initiating', max_length=15)),
                ('started_at', models.DateTimeField(auto_now_add=True)),
                ('answered_at', models.DateTimeField(blank=True, null=True)),
                ('ended_at', models.DateTimeField(blank=True, null=True)),
                ('duration', models.DurationField(blank=True, null=True)),
                ('end_reason', models.CharField(blank=True, max_length=50)),
                ('caller_ice_candidates', models.JSONField(blank=True, default=list)),
                ('callee_ice_candidates', models.JSONField(blank=True, default=list)),
                ('webrtc_offer', models.JSONField(blank=True, null=True)),
                ('webrtc_answer', models.JSONField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('callee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='received_calls', to=settings.AUTH_USER_MODEL)),
                ('caller', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='initiated_calls', to=settings.AUTH_USER_MODEL)),
                ('conversation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='call_sessions', to='communications.conversation')),
            ],
            options={
                'db_table': 'communications_call_sessions',
                'ordering': ['-started_at'],
            },
        ),
        migrations.AddField(
            model_name='userpresence',
            name='current_call_session',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='active_presence', to='communications.callsession'),
        ),
        migrations.CreateModel(
            name='UserCallSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('voice_calls_enabled', models.BooleanField(default=True)),
                ('video_calls_enabled', models.BooleanField(default=True)),
                ('auto_reject_calls', models.BooleanField(default=False)),
                ('call_notifications_enabled', models.BooleanField(default=True)),
                ('preferred_video_quality', models.CharField(choices=[('low', 'Low (240p)'), ('medium', 'Medium (480p)'), ('high', 'High (720p)'), ('hd', 'HD (1080p)')], default='medium', max_length=10)),
                ('allow_calls_from_strangers', models.BooleanField(default=False)),
                ('show_call_history', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='call_settings', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'communications_user_call_settings',
            },
        ),
        migrations.AddIndex(
            model_name='callsession',
            index=models.Index(fields=['caller', '-started_at'], name='communicati_caller__bd4b85_idx'),
        ),
        migrations.AddIndex(
            model_name='callsession',
            index=models.Index(fields=['callee', '-started_at'], name='communicati_callee__70dc82_idx'),
        ),
        migrations.AddIndex(
            model_name='callsession',
            index=models.Index(fields=['conversation', '-started_at'], name='communicati_convers_e705b2_idx'),
        ),
        migrations.AddIndex(
            model_name='callsession',
            index=models.Index(fields=['state'], name='communicati_state_50fecc_idx'),
        ),
    ]
