"""
Django management command to test WebRTC configuration
Usage: python manage.py test_webrtc_config
"""

from django.core.management.base import BaseCommand
from django.conf import settings
from communications.webrtc_config import WebRTCConfig, RECOMMENDED_TURN_PROVIDERS
import json


class Command(BaseCommand):
    help = 'Test and validate WebRTC configuration for HeartGrid Communications'

    def add_arguments(self, parser):
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Show detailed configuration information',
        )
        parser.add_argument(
            '--json',
            action='store_true',
            help='Output configuration as JSON',
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('=== HeartGrid WebRTC Configuration Test ===\n')
        )

        # Validate configuration
        is_valid, errors = WebRTCConfig.validate_configuration()
        
        if is_valid:
            self.stdout.write(
                self.style.SUCCESS('✓ WebRTC configuration is valid')
            )
        else:
            self.stdout.write(
                self.style.ERROR('✗ WebRTC configuration has errors:')
            )
            for error in errors:
                self.stdout.write(f'  - {error}')
            self.stdout.write('')

        # Show configuration details
        if options['verbose'] or not is_valid:
            self.show_configuration_details(options['json'])

        # Show TURN server status
        self.show_turn_status()

        # Show recommendations
        if not WebRTCConfig.is_turn_required():
            self.show_turn_recommendations()

    def show_configuration_details(self, as_json=False):
        """Show detailed WebRTC configuration"""
        config = WebRTCConfig.get_webrtc_configuration()
        
        if as_json:
            self.stdout.write(json.dumps(config, indent=2))
            return

        self.stdout.write('\n=== Configuration Details ===')
        
        # ICE Servers
        self.stdout.write(f'\nICE Servers ({len(config["iceServers"])} configured):')
        for i, server in enumerate(config['iceServers'], 1):
            server_type = 'TURN' if 'username' in server else 'STUN'
            self.stdout.write(f'  {i}. {server_type}: {server["urls"]}')
            if 'username' in server:
                self.stdout.write(f'     Username: {server["username"]}')
                self.stdout.write(f'     Credential: {"*" * len(server.get("credential", ""))}')

        # Other configuration
        self.stdout.write(f'\nOther Settings:')
        self.stdout.write(f'  ICE Candidate Pool Size: {config["iceCandidatePoolSize"]}')
        self.stdout.write(f'  Bundle Policy: {config["bundlePolicy"]}')
        self.stdout.write(f'  RTCP Mux Policy: {config["rtcpMuxPolicy"]}')
        self.stdout.write(f'  ICE Transport Policy: {config["iceTransportPolicy"]}')

        # Media constraints
        media_constraints = WebRTCConfig.get_media_constraints()
        self.stdout.write(f'\nMedia Constraints:')
        self.stdout.write(f'  Audio: {json.dumps(media_constraints["audio"], indent=4)}')
        self.stdout.write(f'  Video: {json.dumps(media_constraints["video"], indent=4)}')

    def show_turn_status(self):
        """Show TURN server configuration status"""
        turn_servers = WebRTCConfig.get_turn_servers()
        
        self.stdout.write('\n=== TURN Server Status ===')
        
        if turn_servers:
            self.stdout.write(
                self.style.SUCCESS(f'✓ {len(turn_servers)} TURN server(s) configured')
            )
            self.stdout.write('  This enables calling through NAT/firewalls')
        else:
            self.stdout.write(
                self.style.WARNING('⚠ No TURN servers configured')
            )
            self.stdout.write('  Calls may fail for users behind NAT/firewalls')

    def show_turn_recommendations(self):
        """Show TURN server provider recommendations"""
        self.stdout.write('\n=== TURN Server Recommendations ===')
        self.stdout.write('For production deployment, consider these TURN providers:\n')
        
        for provider_id, provider in RECOMMENDED_TURN_PROVIDERS.items():
            self.stdout.write(f'• {provider["name"]}')
            self.stdout.write(f'  {provider["description"]}')
            self.stdout.write(f'  URL: {provider["url"]}\n')

        self.stdout.write('Configuration examples:')
        self.stdout.write('\n1. Environment Variables:')
        self.stdout.write('   export TURN_SERVER_URL="turn:your-turn-server.com:3478"')
        self.stdout.write('   export TURN_USERNAME="your-username"')
        self.stdout.write('   export TURN_CREDENTIAL="your-password"')
        
        self.stdout.write('\n2. Django Settings:')
        self.stdout.write('   WEBRTC_TURN_SERVERS = [')
        self.stdout.write('       {')
        self.stdout.write('           "urls": "turn:your-turn-server.com:3478",')
        self.stdout.write('           "username": "your-username",')
        self.stdout.write('           "credential": "your-password",')
        self.stdout.write('           "credentialType": "password"')
        self.stdout.write('       }')
        self.stdout.write('   ]')

        self.stdout.write('\n3. Multiple TURN servers (comma-separated):')
        self.stdout.write('   export TURN_SERVER_URLS="turn:server1.com:3478,turn:server2.com:3478"')
        self.stdout.write('   export TURN_USERNAMES="user1,user2"')
        self.stdout.write('   export TURN_CREDENTIALS="pass1,pass2"')

    def show_environment_check(self):
        """Show current environment variable status"""
        self.stdout.write('\n=== Environment Variables ===')
        
        env_vars = [
            'TURN_SERVER_URL',
            'TURN_USERNAME', 
            'TURN_CREDENTIAL',
            'TURN_SERVER_URLS',
            'TURN_USERNAMES',
            'TURN_CREDENTIALS'
        ]
        
        for var in env_vars:
            value = getattr(settings, var, None) or os.getenv(var)
            if value:
                if 'CREDENTIAL' in var or 'PASSWORD' in var:
                    value = '*' * len(value)
                self.stdout.write(f'  {var}: {value}')
            else:
                self.stdout.write(f'  {var}: Not set')
