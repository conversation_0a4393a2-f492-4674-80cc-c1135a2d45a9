#!/usr/bin/env python
"""
Test script to verify template fixes for gamification, notifications, and subscription
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'heartgrid_django.settings')
django.setup()

from django.test import TestCase, Client
from django.contrib.auth import get_user_model
from django.template import Template, Context
from django.template.loader import get_template
from heartgrid.models import User, Subscription, Achievement, UserActivity, Notification
from django.utils import timezone
from datetime import timedelta

User = get_user_model()

def test_template_syntax():
    """Test that templates have correct syntax"""
    print("🧪 Testing template syntax...")
    
    try:
        # Test gamification template
        template = get_template('gamification.html')
        print("  ✅ Gamification template syntax is valid")
        
        # Test subscription template
        template = get_template('subscription.html')
        print("  ✅ Subscription template syntax is valid")
        
        # Test notifications template
        template = get_template('notifications.html')
        print("  ✅ Notifications template syntax is valid")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Template syntax error: {e}")
        return False

def test_template_rendering():
    """Test that templates render correctly with context"""
    print("\n🧪 Testing template rendering...")
    
    try:
        # Create test user
        user = User.objects.create_user(
            username='templatetest',
            email='<EMAIL>',
            password='testpass123',
            name='Template Test'
        )
        
        # Test gamification template rendering
        template = get_template('gamification.html')
        context = Context({
            'request': type('obj', (object,), {'user': user}),
            'gamification_data': {
                'level': 2,
                'total_points': 150,
                'level_progress': 75,
                'points_to_next_level': 50,
                'achievements': [],
                'rewards_inventory': {
                    'super_likes': 3,
                    'boost_credits': 1
                },
                'statistics': {
                    'total_matches': 5,
                    'total_likes_sent': 20,
                    'total_messages_sent': 15
                }
            },
            'daily_challenge': {
                'day': 'Monday',
                'task': 'Send Likes',
                'target': 5,
                'progress': 2,
                'reward': 'Super Like',
                'progress_percent': 40
            },
            'leaderboard': [
                {
                    'user_id': user.id,
                    'name': 'Template Test',
                    'level': 2,
                    'points': 150,
                    'achievements_count': 0
                }
            ]
        })
        
        rendered = template.render(context)
        print("  ✅ Gamification template renders successfully")
        
        # Test subscription template rendering
        template = get_template('subscription.html')
        context = Context({
            'request': type('obj', (object,), {'user': user}),
            'subscription': None,
            'can_chat': False
        })
        
        rendered = template.render(context)
        print("  ✅ Subscription template renders successfully")
        
        # Test notifications template rendering
        template = get_template('notifications.html')
        context = Context({
            'request': type('obj', (object,), {'user': user}),
            'notifications': [],
            'unread_count': 0,
            'preferences': {
                'new_match': True,
                'new_message': True,
                'new_like': True,
                'super_like_received': True,
                'achievement_unlocked': True,
                'daily_challenge': True,
                'push_enabled': True,
                'email_enabled': False,
            }
        })
        
        rendered = template.render(context)
        print("  ✅ Notifications template renders successfully")
        
        # Clean up
        user.delete()
        print("  ✅ Template test data cleaned up")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Template rendering error: {e}")
        return False

def test_view_responses():
    """Test that views return successful responses"""
    print("\n🧪 Testing view responses...")

    try:
        client = Client()

        # Create test user
        user = User.objects.create_user(
            username='viewresponsetest',
            email='<EMAIL>',
            password='testpass123',
            name='View Response Test'
        )

        # Create some test notifications to avoid empty queryset issues
        Notification.objects.create(
            user=user,
            title='Test Notification',
            message='This is a test notification',
            notification_type='system',
            is_read=False
        )

        # Login user
        client.login(username='viewresponsetest', password='testpass123')
        
        # Test gamification page
        response = client.get('/gamification/')
        if response.status_code == 200:
            print("  ✅ Gamification page returns 200 OK")
        else:
            print(f"  ❌ Gamification page returns {response.status_code}")
            return False
        
        # Test subscription page
        response = client.get('/subscription/')
        if response.status_code == 200:
            print("  ✅ Subscription page returns 200 OK")
        else:
            print(f"  ❌ Subscription page returns {response.status_code}")
            return False
        
        # Test notifications page
        response = client.get('/notifications/')
        if response.status_code == 200:
            print("  ✅ Notifications page returns 200 OK")
        else:
            print(f"  ❌ Notifications page returns {response.status_code}")
            return False
        
        # Clean up
        user.delete()
        print("  ✅ View response test data cleaned up")
        
        return True
        
    except Exception as e:
        print(f"  ❌ View response error: {e}")
        return False

def main():
    """Run all template tests"""
    print("🚀 HeartGrid Template Fixes Test Suite")
    print("=" * 50)
    
    tests = [
        test_template_syntax,
        test_template_rendering,
        test_view_responses
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
            failed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All template tests passed! Template fixes are working correctly.")
    else:
        print("⚠️  Some template tests failed. Please check the errors above.")
    
    return failed == 0

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
