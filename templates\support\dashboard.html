{% extends 'base.html' %}
{% load static %}

{% block title %}Support Center - HeartGrid{% endblock %}

{% block extra_css %}
<style>
    .support-card {
        transition: transform 0.2s;
        border: none;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    .support-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    }
    .support-icon {
        font-size: 2.5rem;
        margin-bottom: 1rem;
    }
    .quick-action {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 1.5rem;
        text-decoration: none;
        display: block;
        transition: all 0.3s;
    }
    .quick-action:hover {
        color: white;
        transform: scale(1.02);
    }
    .status-badge {
        font-size: 0.8rem;
        padding: 0.3rem 0.8rem;
        border-radius: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">Support Center</h1>
                    <p class="text-muted">Get help with your HeartGrid experience</p>
                </div>
                <div>
                    <a href="{% url 'support:create_ticket' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>New Ticket
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card support-card text-center p-3">
                <div class="support-icon text-primary">
                    <i class="fas fa-ticket-alt"></i>
                </div>
                <h4 class="mb-1">{{ open_tickets }}</h4>
                <p class="text-muted mb-0">Open Tickets</p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card support-card text-center p-3">
                <div class="support-icon text-success">
                    <i class="fas fa-check-circle"></i>
                </div>
                <h4 class="mb-1">{{ total_tickets }}</h4>
                <p class="text-muted mb-0">Total Tickets</p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card support-card text-center p-3">
                <div class="support-icon text-info">
                    <i class="fas fa-comments"></i>
                </div>
                <h4 class="mb-1">24/7</h4>
                <p class="text-muted mb-0">Live Chat</p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card support-card text-center p-3">
                <div class="support-icon text-warning">
                    <i class="fas fa-clock"></i>
                </div>
                <h4 class="mb-1">&lt; 2h</h4>
                <p class="text-muted mb-0">Avg Response</p>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <h4 class="mb-3">Quick Actions</h4>
        </div>
        <div class="col-md-4 mb-3">
            <a href="{% url 'support:create_ticket' %}" class="quick-action">
                <i class="fas fa-plus-circle mb-2" style="font-size: 2rem;"></i>
                <h5 class="mb-1">Create Support Ticket</h5>
                <p class="mb-0 opacity-75">Get help with account, billing, or technical issues</p>
            </a>
        </div>
        <div class="col-md-4 mb-3">
            <a href="{% url 'support:faq' %}" class="quick-action">
                <i class="fas fa-question-circle mb-2" style="font-size: 2rem;"></i>
                <h5 class="mb-1">Browse FAQ</h5>
                <p class="mb-0 opacity-75">Find answers to common questions</p>
            </a>
        </div>
        <div class="col-md-4 mb-3">
            <a href="{% url 'support:safety_report' %}" class="quick-action">
                <i class="fas fa-shield-alt mb-2" style="font-size: 2rem;"></i>
                <h5 class="mb-1">Report Safety Issue</h5>
                <p class="mb-0 opacity-75">Report inappropriate behavior or content</p>
            </a>
        </div>
    </div>

    <!-- Recent Tickets -->
    {% if recent_tickets %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Recent Tickets</h5>
                    <a href="{% url 'support:my_tickets' %}" class="btn btn-sm btn-outline-primary">View All</a>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>Ticket #</th>
                                    <th>Subject</th>
                                    <th>Status</th>
                                    <th>Created</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for ticket in recent_tickets %}
                                <tr>
                                    <td>
                                        <code>{{ ticket.ticket_number }}</code>
                                    </td>
                                    <td>
                                        <strong>{{ ticket.subject|truncatechars:50 }}</strong>
                                        <br>
                                        <small class="text-muted">{{ ticket.category.name }}</small>
                                    </td>
                                    <td>
                                        {% if ticket.status == 'open' %}
                                            <span class="badge bg-primary status-badge">{{ ticket.get_status_display }}</span>
                                        {% elif ticket.status == 'in_progress' %}
                                            <span class="badge bg-warning status-badge">{{ ticket.get_status_display }}</span>
                                        {% elif ticket.status == 'resolved' %}
                                            <span class="badge bg-success status-badge">{{ ticket.get_status_display }}</span>
                                        {% else %}
                                            <span class="badge bg-secondary status-badge">{{ ticket.get_status_display }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small>{{ ticket.created_at|date:"M d, Y" }}</small>
                                        <br>
                                        <small class="text-muted">{{ ticket.created_at|time:"H:i" }}</small>
                                    </td>
                                    <td>
                                        <a href="{% url 'support:ticket_detail' ticket.id %}" class="btn btn-sm btn-outline-primary">
                                            View
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Support Categories -->
    <div class="row">
        <div class="col-12">
            <h4 class="mb-3">Support Categories</h4>
        </div>
        {% for category in categories %}
        <div class="col-md-6 col-lg-4 mb-3">
            <div class="card support-card h-100">
                <div class="card-body">
                    <div class="d-flex align-items-start">
                        <div class="me-3">
                            {% if category.category_type == 'account' %}
                                <i class="fas fa-user-circle text-primary" style="font-size: 1.5rem;"></i>
                            {% elif category.category_type == 'billing' %}
                                <i class="fas fa-credit-card text-success" style="font-size: 1.5rem;"></i>
                            {% elif category.category_type == 'safety' %}
                                <i class="fas fa-shield-alt text-danger" style="font-size: 1.5rem;"></i>
                            {% elif category.category_type == 'technical' %}
                                <i class="fas fa-cog text-info" style="font-size: 1.5rem;"></i>
                            {% else %}
                                <i class="fas fa-question-circle text-muted" style="font-size: 1.5rem;"></i>
                            {% endif %}
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="card-title mb-2">{{ category.name }}</h6>
                            <p class="card-text text-muted small">{{ category.description|truncatechars:100 }}</p>
                            <a href="{% url 'support:create_ticket' %}?category={{ category.id }}" 
                               class="btn btn-sm btn-outline-primary">
                                Get Help
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Contact Info -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card bg-light">
                <div class="card-body text-center">
                    <h5>Need Immediate Help?</h5>
                    <p class="text-muted">Our support team is available 24/7 to assist you</p>
                    <div class="row">
                        <div class="col-md-4">
                            <i class="fas fa-envelope text-primary mb-2" style="font-size: 1.5rem;"></i>
                            <p class="mb-0"><strong>Email Support</strong></p>
                            <p class="text-muted"><EMAIL></p>
                        </div>
                        <div class="col-md-4">
                            <i class="fas fa-comments text-success mb-2" style="font-size: 1.5rem;"></i>
                            <p class="mb-0"><strong>Live Chat</strong></p>
                            <p class="text-muted">Available 24/7</p>
                        </div>
                        <div class="col-md-4">
                            <i class="fas fa-phone text-info mb-2" style="font-size: 1.5rem;"></i>
                            <p class="mb-0"><strong>Emergency</strong></p>
                            <p class="text-muted">For urgent safety issues</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
