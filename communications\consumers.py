"""
WebSocket Consumers for HeartGrid Communications

This module contains WebSocket consumers for real-time messaging,
user presence tracking, and notifications.
"""

import json
import uuid
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from django.contrib.auth import get_user_model
from django.utils import timezone
from .models import (
    Conversation, Message, UserPresence, MessageStatus,
    ConversationParticipant, CallSession, UserCallSettings
)
from .notifications import (
    notify_incoming_call, notify_call_state_change,
    notify_call_availability_change, notify_presence_change
)

User = get_user_model()


class ChatConsumer(AsyncWebsocketConsumer):
    """
    WebSocket consumer for handling real-time chat messages
    """

    async def connect(self):
        """Handle WebSocket connection"""
        self.user = self.scope["user"]
        
        if not self.user.is_authenticated:
            await self.close()
            return

        # Get conversation ID from URL
        self.conversation_id = self.scope['url_route']['kwargs']['conversation_id']
        self.room_group_name = f'chat_{self.conversation_id}'

        # Verify user is participant in conversation
        if not await self.is_participant():
            await self.close()
            return

        # Join room group
        await self.channel_layer.group_add(
            self.room_group_name,
            self.channel_name
        )

        # Set user as online
        await self.set_user_online()

        await self.accept()

        # Send initial conversation data
        await self.send_conversation_data()

    async def disconnect(self, close_code):
        """Handle WebSocket disconnection"""
        if hasattr(self, 'room_group_name'):
            # Leave room group
            await self.channel_layer.group_discard(
                self.room_group_name,
                self.channel_name
            )

        # Update user presence
        if hasattr(self, 'user') and self.user.is_authenticated:
            await self.set_user_offline()

    async def receive(self, text_data):
        """Handle incoming WebSocket messages"""
        try:
            data = json.loads(text_data)
            message_type = data.get('type')

            if message_type == 'chat_message':
                await self.handle_chat_message(data)
            elif message_type == 'typing_start':
                await self.handle_typing_start(data)
            elif message_type == 'typing_stop':
                await self.handle_typing_stop(data)
            elif message_type == 'mark_read':
                await self.handle_mark_read(data)
            elif message_type == 'message_reaction':
                await self.handle_message_reaction(data)
            # WebRTC call signaling handlers
            elif message_type == 'call_initiate':
                await self.handle_call_initiate(data)
            elif message_type == 'call_answer':
                await self.handle_call_answer(data)
            elif message_type == 'call_reject':
                await self.handle_call_reject(data)
            elif message_type == 'call_end':
                await self.handle_call_end(data)
            elif message_type == 'webrtc_offer':
                await self.handle_webrtc_offer(data)
            elif message_type == 'webrtc_answer':
                await self.handle_webrtc_answer(data)
            elif message_type == 'webrtc_ice_candidate':
                await self.handle_ice_candidate(data)

        except json.JSONDecodeError:
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': 'Invalid JSON format'
            }))

    async def handle_chat_message(self, data):
        """Handle incoming chat messages"""
        content = data.get('message', '').strip()
        message_type = data.get('message_type', 'text')
        
        if not content and message_type == 'text':
            return

        # Create message in database
        message = await self.create_message(content, message_type)
        
        if message:
            # Send message to room group
            await self.channel_layer.group_send(
                self.room_group_name,
                {
                    'type': 'chat_message',
                    'message': await self.serialize_message(message),
                    'sender_id': str(self.user.id)
                }
            )

    async def handle_typing_start(self, data):
        """Handle typing start indicator"""
        await self.set_typing_status(True)
        
        # Broadcast typing status to other participants
        await self.channel_layer.group_send(
            self.room_group_name,
            {
                'type': 'typing_status',
                'user_id': str(self.user.id),
                'user_name': self.user.name,
                'is_typing': True
            }
        )

    async def handle_typing_stop(self, data):
        """Handle typing stop indicator"""
        await self.set_typing_status(False)
        
        # Broadcast typing status to other participants
        await self.channel_layer.group_send(
            self.room_group_name,
            {
                'type': 'typing_status',
                'user_id': str(self.user.id),
                'user_name': self.user.name,
                'is_typing': False
            }
        )

    async def handle_mark_read(self, data):
        """Handle marking messages as read"""
        message_id = data.get('message_id')
        if message_id:
            await self.mark_message_read(message_id)

    async def handle_message_reaction(self, data):
        """Handle message reactions (emoji)"""
        message_id = data.get('message_id')
        emoji = data.get('emoji')
        action = data.get('action', 'add')  # 'add' or 'remove'
        
        if message_id and emoji:
            await self.update_message_reaction(message_id, emoji, action)

    # WebSocket message handlers
    async def chat_message(self, event):
        """Send chat message to WebSocket"""
        await self.send(text_data=json.dumps({
            'type': 'chat_message',
            'message': event['message'],
            'sender_id': event['sender_id']
        }))

    async def typing_status(self, event):
        """Send typing status to WebSocket"""
        # Don't send typing status to the user who is typing
        if event['user_id'] != str(self.user.id):
            await self.send(text_data=json.dumps({
                'type': 'typing_status',
                'user_id': event['user_id'],
                'user_name': event['user_name'],
                'is_typing': event['is_typing']
            }))

    async def user_status(self, event):
        """Send user status update to WebSocket"""
        await self.send(text_data=json.dumps({
            'type': 'user_status',
            'user_id': event['user_id'],
            'status': event['status'],
            'last_seen': event.get('last_seen')
        }))

    async def message_reaction(self, event):
        """Send message reaction update to WebSocket"""
        await self.send(text_data=json.dumps({
            'type': 'message_reaction',
            'message_id': event['message_id'],
            'reactions': event['reactions']
        }))

    # WebRTC Call WebSocket message handlers
    async def incoming_call(self, event):
        """Send incoming call notification to WebSocket"""
        await self.send(text_data=json.dumps({
            'type': 'incoming_call',
            'call_session_id': event['call_session_id'],
            'caller_id': event['caller_id'],
            'caller_name': event['caller_name'],
            'call_type': event['call_type'],
            'conversation_id': event['conversation_id']
        }))

    async def call_answered(self, event):
        """Send call answered notification to WebSocket"""
        await self.send(text_data=json.dumps({
            'type': 'call_answered',
            'call_session_id': event['call_session_id'],
            'callee_id': event['callee_id']
        }))

    async def call_rejected(self, event):
        """Send call rejected notification to WebSocket"""
        await self.send(text_data=json.dumps({
            'type': 'call_rejected',
            'call_session_id': event['call_session_id'],
            'callee_id': event['callee_id']
        }))

    async def call_ended(self, event):
        """Send call ended notification to WebSocket"""
        await self.send(text_data=json.dumps({
            'type': 'call_ended',
            'call_session_id': event['call_session_id'],
            'ended_by': event['ended_by'],
            'reason': event['reason']
        }))

    async def webrtc_offer(self, event):
        """Send WebRTC offer to WebSocket"""
        await self.send(text_data=json.dumps({
            'type': 'webrtc_offer',
            'offer': event['offer'],
            'call_session_id': event['call_session_id'],
            'caller_id': event['caller_id']
        }))

    async def webrtc_answer(self, event):
        """Send WebRTC answer to WebSocket"""
        await self.send(text_data=json.dumps({
            'type': 'webrtc_answer',
            'answer': event['answer'],
            'call_session_id': event['call_session_id'],
            'callee_id': event['callee_id']
        }))

    async def webrtc_ice_candidate(self, event):
        """Send WebRTC ICE candidate to WebSocket"""
        await self.send(text_data=json.dumps({
            'type': 'webrtc_ice_candidate',
            'candidate': event['candidate'],
            'call_session_id': event['call_session_id'],
            'sender_id': event['sender_id']
        }))

    # Database operations
    @database_sync_to_async
    def is_participant(self):
        """Check if user is a participant in the conversation"""
        try:
            conversation = Conversation.objects.get(id=self.conversation_id)
            return conversation.participants.filter(id=self.user.id).exists()
        except Conversation.DoesNotExist:
            return False

    @database_sync_to_async
    def create_message(self, content, message_type='text'):
        """Create a new message in the database"""
        try:
            conversation = Conversation.objects.get(id=self.conversation_id)
            message = Message.objects.create(
                conversation=conversation,
                sender=self.user,
                content=content,
                message_type=message_type
            )
            
            # Create message status for all participants except sender
            participants = conversation.participants.exclude(id=self.user.id)
            for participant in participants:
                MessageStatus.objects.create(
                    message=message,
                    user=participant,
                    status='sent'
                )
            
            return message
        except Conversation.DoesNotExist:
            return None

    @database_sync_to_async
    def serialize_message(self, message):
        """Serialize message for WebSocket transmission"""
        return {
            'id': str(message.id),
            'content': message.content,
            'message_type': message.message_type,
            'sender_id': str(message.sender.id),
            'sender_name': message.sender.name,
            'created_at': message.created_at.isoformat(),
            'reactions': message.reactions
        }

    @database_sync_to_async
    def set_user_online(self):
        """Set user status to online"""
        presence, created = UserPresence.objects.get_or_create(user=self.user)
        presence.set_online()

    @database_sync_to_async
    def set_user_offline(self):
        """Set user status to offline"""
        try:
            presence = UserPresence.objects.get(user=self.user)
            presence.set_offline()
        except UserPresence.DoesNotExist:
            pass

    @database_sync_to_async
    def set_typing_status(self, is_typing):
        """Set typing status for user"""
        try:
            presence = UserPresence.objects.get(user=self.user)
            if is_typing:
                # Get the other participant
                conversation = Conversation.objects.get(id=self.conversation_id)
                other_user = conversation.get_other_participant(self.user)
                if other_user:
                    presence.set_typing(other_user)
            else:
                presence.clear_typing()
        except (UserPresence.DoesNotExist, Conversation.DoesNotExist):
            pass

    @database_sync_to_async
    def mark_message_read(self, message_id):
        """Mark a message as read"""
        try:
            message = Message.objects.get(id=message_id)
            status, created = MessageStatus.objects.get_or_create(
                message=message,
                user=self.user,
                defaults={'status': 'read', 'read_at': timezone.now()}
            )
            if not created and status.status != 'read':
                status.mark_as_read()
        except Message.DoesNotExist:
            pass

    @database_sync_to_async
    def update_message_reaction(self, message_id, emoji, action):
        """Add or remove message reaction"""
        try:
            message = Message.objects.get(id=message_id)
            if action == 'add':
                message.add_reaction(self.user, emoji)
            elif action == 'remove':
                message.remove_reaction(self.user)
            
            # Broadcast reaction update
            return message.reactions
        except Message.DoesNotExist:
            return None

    # WebRTC Call Signaling Methods

    async def handle_call_initiate(self, data):
        """Handle call initiation"""
        call_type = data.get('call_type', 'voice')
        callee_id = data.get('callee_id')

        if not callee_id:
            await self.send_error('Missing callee_id')
            return

        try:
            # Get conversation and verify participants
            conversation = await self.get_conversation()
            callee = await self.get_user_by_id(callee_id)

            if not callee or not await self.is_conversation_participant(callee):
                await self.send_error('Invalid callee or not in conversation')
                return

            # Check if callee can receive calls
            if not await self.can_user_receive_calls(callee, call_type):
                await self.send_error('User cannot receive calls at this time')
                return

            # Create call session
            call_session = await self.create_call_session(callee, call_type)

            # Update caller presence to in_call
            await self.set_user_in_call(call_session)

            # Send incoming call notification using notification system
            await database_sync_to_async(notify_incoming_call)(call_session)

            # Send call notification to callee via WebSocket
            await self.channel_layer.group_send(
                f'user_{callee_id}',
                {
                    'type': 'incoming_call',
                    'call_session_id': str(call_session.id),
                    'caller_id': str(self.user.id),
                    'caller_name': self.user.name,
                    'call_type': call_type,
                    'conversation_id': str(self.conversation_id)
                }
            )

            # Confirm call initiation to caller
            await self.send(text_data=json.dumps({
                'type': 'call_initiated',
                'call_session_id': str(call_session.id),
                'callee_id': callee_id,
                'call_type': call_type
            }))

        except Exception as e:
            await self.send_error(f'Failed to initiate call: {str(e)}')

    async def handle_call_answer(self, data):
        """Handle call answer"""
        call_session_id = data.get('call_session_id')

        if not call_session_id:
            await self.send_error('Missing call_session_id')
            return

        try:
            call_session = await self.get_call_session(call_session_id)

            if not call_session or call_session.callee != self.user:
                await self.send_error('Invalid call session')
                return

            # Mark call as answered
            previous_state = call_session.call_state
            await self.mark_call_answered(call_session)

            # Update callee presence to in_call
            await self.set_user_in_call(call_session)

            # Send call state change notification
            await database_sync_to_async(notify_call_state_change)(call_session, previous_state)

            # Notify caller that call was answered via WebSocket
            await self.channel_layer.group_send(
                f'user_{call_session.caller.id}',
                {
                    'type': 'call_answered',
                    'call_session_id': str(call_session.id),
                    'callee_id': str(self.user.id)
                }
            )

            # Confirm to callee
            await self.send(text_data=json.dumps({
                'type': 'call_answer_confirmed',
                'call_session_id': str(call_session.id)
            }))

        except Exception as e:
            await self.send_error(f'Failed to answer call: {str(e)}')

    async def handle_call_reject(self, data):
        """Handle call rejection"""
        call_session_id = data.get('call_session_id')

        if not call_session_id:
            await self.send_error('Missing call_session_id')
            return

        try:
            call_session = await self.get_call_session(call_session_id)

            if not call_session or call_session.callee != self.user:
                await self.send_error('Invalid call session')
                return

            # Mark call as rejected
            await self.mark_call_rejected(call_session)

            # Notify caller that call was rejected
            await self.channel_layer.group_send(
                f'user_{call_session.caller.id}',
                {
                    'type': 'call_rejected',
                    'call_session_id': str(call_session.id),
                    'callee_id': str(self.user.id)
                }
            )

            # Reset caller presence
            await self.set_user_available_for_calls()

        except Exception as e:
            await self.send_error(f'Failed to reject call: {str(e)}')

    async def handle_call_end(self, data):
        """Handle call end"""
        call_session_id = data.get('call_session_id')
        end_reason = data.get('reason', 'normal')

        if not call_session_id:
            await self.send_error('Missing call_session_id')
            return

        try:
            call_session = await self.get_call_session(call_session_id)

            if not call_session or self.user not in [call_session.caller, call_session.callee]:
                await self.send_error('Invalid call session')
                return

            # Mark call as ended
            await self.mark_call_ended(call_session, end_reason)

            # Get the other participant
            other_user = call_session.callee if call_session.caller == self.user else call_session.caller

            # Notify other participant that call ended
            await self.channel_layer.group_send(
                f'user_{other_user.id}',
                {
                    'type': 'call_ended',
                    'call_session_id': str(call_session.id),
                    'ended_by': str(self.user.id),
                    'reason': end_reason
                }
            )

            # Reset both users' presence
            await self.set_user_available_for_calls()
            await self.set_other_user_available_for_calls(other_user)

        except Exception as e:
            await self.send_error(f'Failed to end call: {str(e)}')

    async def handle_webrtc_offer(self, data):
        """Handle WebRTC offer for peer connection"""
        call_session_id = data.get('call_session_id')
        offer = data.get('offer')

        if not call_session_id or not offer:
            await self.send_error('Missing call_session_id or offer')
            return

        try:
            call_session = await self.get_call_session(call_session_id)

            if not call_session or call_session.caller != self.user:
                await self.send_error('Invalid call session for offer')
                return

            # Store WebRTC offer
            await self.store_webrtc_offer(call_session, offer)

            # Forward offer to callee
            await self.channel_layer.group_send(
                f'user_{call_session.callee.id}',
                {
                    'type': 'webrtc_offer',
                    'offer': offer,
                    'call_session_id': str(call_session.id),
                    'caller_id': str(self.user.id)
                }
            )

        except Exception as e:
            await self.send_error(f'Failed to handle WebRTC offer: {str(e)}')

    async def handle_webrtc_answer(self, data):
        """Handle WebRTC answer for peer connection"""
        call_session_id = data.get('call_session_id')
        answer = data.get('answer')

        if not call_session_id or not answer:
            await self.send_error('Missing call_session_id or answer')
            return

        try:
            call_session = await self.get_call_session(call_session_id)

            if not call_session or call_session.callee != self.user:
                await self.send_error('Invalid call session for answer')
                return

            # Store WebRTC answer
            await self.store_webrtc_answer(call_session, answer)

            # Forward answer to caller
            await self.channel_layer.group_send(
                f'user_{call_session.caller.id}',
                {
                    'type': 'webrtc_answer',
                    'answer': answer,
                    'call_session_id': str(call_session.id),
                    'callee_id': str(self.user.id)
                }
            )

        except Exception as e:
            await self.send_error(f'Failed to handle WebRTC answer: {str(e)}')

    async def handle_ice_candidate(self, data):
        """Handle ICE candidate for WebRTC connection"""
        call_session_id = data.get('call_session_id')
        candidate = data.get('candidate')

        if not call_session_id or not candidate:
            await self.send_error('Missing call_session_id or candidate')
            return

        try:
            call_session = await self.get_call_session(call_session_id)

            if not call_session or self.user not in [call_session.caller, call_session.callee]:
                await self.send_error('Invalid call session for ICE candidate')
                return

            # Store ICE candidate
            is_caller = call_session.caller == self.user
            await self.store_ice_candidate(call_session, candidate, is_caller)

            # Forward ICE candidate to other participant
            other_user = call_session.callee if is_caller else call_session.caller
            await self.channel_layer.group_send(
                f'user_{other_user.id}',
                {
                    'type': 'webrtc_ice_candidate',
                    'candidate': candidate,
                    'call_session_id': str(call_session.id),
                    'sender_id': str(self.user.id)
                }
            )

        except Exception as e:
            await self.send_error(f'Failed to handle ICE candidate: {str(e)}')

    # Database helper methods for call functionality

    @database_sync_to_async
    def get_user_by_id(self, user_id):
        """Get user by ID"""
        try:
            return User.objects.get(id=user_id)
        except User.DoesNotExist:
            return None

    @database_sync_to_async
    def get_conversation(self):
        """Get current conversation"""
        try:
            return Conversation.objects.get(id=self.conversation_id)
        except Conversation.DoesNotExist:
            return None

    @database_sync_to_async
    def is_conversation_participant(self, user):
        """Check if user is participant in conversation"""
        try:
            conversation = Conversation.objects.get(id=self.conversation_id)
            return conversation.participants.filter(id=user.id).exists()
        except Conversation.DoesNotExist:
            return False

    @database_sync_to_async
    def can_user_receive_calls(self, user, call_type):
        """Check if user can receive calls"""
        try:
            presence = UserPresence.objects.get(user=user)
            if not presence.can_receive_calls():
                return False

            # Check user call settings
            call_settings, created = UserCallSettings.objects.get_or_create(user=user)
            if call_type == 'voice':
                return call_settings.can_receive_voice_calls()
            elif call_type == 'video':
                return call_settings.can_receive_video_calls()

            return False
        except UserPresence.DoesNotExist:
            return False

    @database_sync_to_async
    def create_call_session(self, callee, call_type):
        """Create a new call session"""
        conversation = Conversation.objects.get(id=self.conversation_id)
        return CallSession.objects.create(
            conversation=conversation,
            caller=self.user,
            callee=callee,
            call_type=call_type,
            state='ringing'
        )

    @database_sync_to_async
    def get_call_session(self, call_session_id):
        """Get call session by ID"""
        try:
            return CallSession.objects.get(id=call_session_id)
        except CallSession.DoesNotExist:
            return None

    @database_sync_to_async
    def mark_call_answered(self, call_session):
        """Mark call as answered"""
        call_session.mark_as_answered()

    @database_sync_to_async
    def mark_call_rejected(self, call_session):
        """Mark call as rejected"""
        call_session.mark_as_rejected()

    @database_sync_to_async
    def mark_call_ended(self, call_session, reason):
        """Mark call as ended"""
        call_session.mark_as_ended(reason)

    @database_sync_to_async
    def set_user_in_call(self, call_session):
        """Set user presence to in_call"""
        presence, created = UserPresence.objects.get_or_create(user=self.user)
        presence.set_in_call(call_session)

    @database_sync_to_async
    def set_user_available_for_calls(self):
        """Set user presence to available for calls"""
        try:
            presence = UserPresence.objects.get(user=self.user)
            presence.set_available_for_calls()
        except UserPresence.DoesNotExist:
            pass

    @database_sync_to_async
    def set_other_user_available_for_calls(self, user):
        """Set other user presence to available for calls"""
        try:
            presence = UserPresence.objects.get(user=user)
            presence.set_available_for_calls()
        except UserPresence.DoesNotExist:
            pass

    @database_sync_to_async
    def store_webrtc_offer(self, call_session, offer):
        """Store WebRTC offer in call session"""
        call_session.set_webrtc_offer(offer)

    @database_sync_to_async
    def store_webrtc_answer(self, call_session, answer):
        """Store WebRTC answer in call session"""
        call_session.set_webrtc_answer(answer)

    @database_sync_to_async
    def store_ice_candidate(self, call_session, candidate, is_caller):
        """Store ICE candidate in call session"""
        call_session.add_ice_candidate(candidate, is_caller)

    async def send_error(self, message):
        """Send error message to client"""
        await self.send(text_data=json.dumps({
            'type': 'error',
            'message': message
        }))


class PresenceConsumer(AsyncWebsocketConsumer):
    """
    WebSocket consumer for handling user presence updates
    """

    async def connect(self):
        """Handle WebSocket connection for presence tracking"""
        self.user = self.scope["user"]

        if not self.user.is_authenticated:
            await self.close()
            return

        # Join user's presence group
        self.user_group_name = f'user_{self.user.id}'
        await self.channel_layer.group_add(
            self.user_group_name,
            self.channel_name
        )

        # Set user as online
        await self.set_user_online()

        await self.accept()

        # Broadcast user online status
        await self.broadcast_presence_update('online')

    async def disconnect(self, close_code):
        """Handle WebSocket disconnection"""
        if hasattr(self, 'user_group_name'):
            await self.channel_layer.group_discard(
                self.user_group_name,
                self.channel_name
            )

        # Set user as offline and broadcast
        if hasattr(self, 'user') and self.user.is_authenticated:
            await self.set_user_offline()
            await self.broadcast_presence_update('offline')

    async def receive(self, text_data):
        """Handle incoming presence messages"""
        try:
            data = json.loads(text_data)
            message_type = data.get('type')

            if message_type == 'heartbeat':
                await self.handle_heartbeat()
            elif message_type == 'status_change':
                await self.handle_status_change(data)

        except json.JSONDecodeError:
            pass

    async def handle_heartbeat(self):
        """Handle heartbeat to keep connection alive"""
        await self.update_last_activity()

    async def handle_status_change(self, data):
        """Handle manual status changes (away, etc.)"""
        status = data.get('status', 'online')
        if status in ['online', 'away']:
            await self.update_user_status(status)
            await self.broadcast_presence_update(status)

    async def presence_update(self, event):
        """Send presence update to WebSocket"""
        await self.send(text_data=json.dumps({
            'type': 'presence_update',
            'user_id': event['user_id'],
            'status': event['status'],
            'last_seen': event.get('last_seen')
        }))

    @database_sync_to_async
    def set_user_online(self):
        """Set user status to online"""
        presence, created = UserPresence.objects.get_or_create(user=self.user)
        presence.set_online()

    @database_sync_to_async
    def set_user_offline(self):
        """Set user status to offline"""
        try:
            presence = UserPresence.objects.get(user=self.user)
            presence.set_offline()
        except UserPresence.DoesNotExist:
            pass

    @database_sync_to_async
    def update_last_activity(self):
        """Update user's last activity timestamp"""
        try:
            presence = UserPresence.objects.get(user=self.user)
            presence.last_activity = timezone.now()
            presence.save()
        except UserPresence.DoesNotExist:
            pass

    @database_sync_to_async
    def update_user_status(self, status):
        """Update user's presence status"""
        try:
            presence = UserPresence.objects.get(user=self.user)
            presence.status = status
            presence.last_activity = timezone.now()
            presence.save()
        except UserPresence.DoesNotExist:
            pass

    async def broadcast_presence_update(self, status):
        """Broadcast presence update to relevant users"""
        # Get users who should receive this update (matches, active conversations)
        relevant_users = await self.get_relevant_users()

        for user_id in relevant_users:
            await self.channel_layer.group_send(
                f'user_{user_id}',
                {
                    'type': 'presence_update',
                    'user_id': str(self.user.id),
                    'status': status,
                    'last_seen': timezone.now().isoformat() if status == 'offline' else None
                }
            )

    @database_sync_to_async
    def get_relevant_users(self):
        """Get list of users who should receive presence updates"""
        # Get users from active conversations
        conversation_users = set()

        # Get conversations where this user is a participant
        conversations = Conversation.objects.filter(
            participants=self.user,
            is_active=True
        ).prefetch_related('participants')

        for conversation in conversations:
            for participant in conversation.participants.exclude(id=self.user.id):
                conversation_users.add(str(participant.id))

        return list(conversation_users)


class NotificationConsumer(AsyncWebsocketConsumer):
    """
    WebSocket consumer for handling general notifications
    """

    async def connect(self):
        """Handle WebSocket connection for notifications"""
        self.user = self.scope["user"]

        if not self.user.is_authenticated:
            await self.close()
            return

        # Join user's notification group
        self.user_group_name = f'user_{self.user.id}'
        await self.channel_layer.group_add(
            self.user_group_name,
            self.channel_name
        )

        await self.accept()

    async def disconnect(self, close_code):
        """Handle WebSocket disconnection"""
        if hasattr(self, 'user_group_name'):
            await self.channel_layer.group_discard(
                self.user_group_name,
                self.channel_name
            )

    async def receive(self, text_data):
        """Handle incoming notification messages"""
        try:
            data = json.loads(text_data)
            message_type = data.get('type')

            if message_type == 'mark_notification_read':
                await self.handle_mark_notification_read(data)

        except json.JSONDecodeError:
            pass

    async def handle_mark_notification_read(self, data):
        """Handle marking notifications as read"""
        notification_id = data.get('notification_id')
        # Implement notification read tracking if needed
        pass

    async def send_notification(self, event):
        """Send notification to WebSocket"""
        await self.send(text_data=json.dumps(event['notification']))

    @database_sync_to_async
    def send_conversation_data(self):
        """Send initial conversation data"""
        try:
            conversation = Conversation.objects.get(id=self.conversation_id)
            # Get recent messages
            messages = conversation.messages.filter(is_deleted=False).order_by('-created_at')[:50]
            
            # Get participant info
            participants = []
            for participant in conversation.participants.all():
                presence = getattr(participant, 'presence', None)
                participants.append({
                    'id': str(participant.id),
                    'name': participant.name,
                    'status': presence.status if presence else 'offline',
                    'last_seen': presence.last_seen.isoformat() if presence and presence.last_seen else None
                })
            
            return {
                'conversation_id': str(conversation.id),
                'participants': participants,
                'messages': [self.serialize_message(msg) for msg in reversed(messages)]
            }
        except Conversation.DoesNotExist:
            return None
