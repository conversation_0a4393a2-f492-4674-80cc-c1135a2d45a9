"""
Serializers for HeartGrid Communications

This module contains DRF serializers for the communications models.
"""

from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import (
    Conversation, Message, UserPresence, MessageStatus, 
    ConversationParticipant
)

User = get_user_model()


class UserBasicSerializer(serializers.ModelSerializer):
    """Basic user serializer for communications"""
    
    class Meta:
        model = User
        fields = ['id', 'name', 'email']
        read_only_fields = ['id', 'name', 'email']


class UserPresenceSerializer(serializers.ModelSerializer):
    """Serializer for user presence information"""
    user = UserBasicSerializer(read_only=True)
    
    class Meta:
        model = UserPresence
        fields = [
            'user', 'status', 'last_seen', 'last_activity', 
            'is_typing_to', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'user', 'last_seen', 'last_activity', 
            'created_at', 'updated_at'
        ]


class MessageStatusSerializer(serializers.ModelSerializer):
    """Serializer for message status"""
    user = UserBasicSerializer(read_only=True)
    
    class Meta:
        model = MessageStatus
        fields = ['user', 'status', 'read_at', 'created_at', 'updated_at']
        read_only_fields = ['user', 'created_at', 'updated_at']


class MessageSerializer(serializers.ModelSerializer):
    """Serializer for messages"""
    sender = UserBasicSerializer(read_only=True)
    statuses = MessageStatusSerializer(many=True, read_only=True)
    
    class Meta:
        model = Message
        fields = [
            'id', 'conversation', 'sender', 'message_type', 'content',
            'file_attachment', 'created_at', 'updated_at', 'edited_at',
            'is_deleted', 'deleted_at', 'reactions', 'statuses'
        ]
        read_only_fields = [
            'id', 'sender', 'created_at', 'updated_at', 'edited_at',
            'is_deleted', 'deleted_at', 'statuses'
        ]

    def create(self, validated_data):
        """Create a new message"""
        validated_data['sender'] = self.context['request'].user
        return super().create(validated_data)


class ConversationParticipantSerializer(serializers.ModelSerializer):
    """Serializer for conversation participants"""
    user = UserBasicSerializer(read_only=True)
    unread_count = serializers.SerializerMethodField()
    
    class Meta:
        model = ConversationParticipant
        fields = [
            'user', 'is_muted', 'is_archived', 'last_read_message',
            'last_read_at', 'notifications_enabled', 'unread_count',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['user', 'unread_count', 'created_at', 'updated_at']

    def get_unread_count(self, obj):
        """Get unread message count for participant"""
        return obj.get_unread_count()


class ConversationSerializer(serializers.ModelSerializer):
    """Serializer for conversations"""
    participants = UserBasicSerializer(many=True, read_only=True)
    participant_settings = ConversationParticipantSerializer(many=True, read_only=True)
    last_message = serializers.SerializerMethodField()
    unread_count = serializers.SerializerMethodField()
    other_participant = serializers.SerializerMethodField()
    
    class Meta:
        model = Conversation
        fields = [
            'id', 'participants', 'participant_settings', 'conversation_type',
            'created_at', 'updated_at', 'last_message_at', 'is_active',
            'is_archived', 'last_message', 'unread_count', 'other_participant'
        ]
        read_only_fields = [
            'id', 'participants', 'participant_settings', 'created_at',
            'updated_at', 'last_message_at', 'last_message', 'unread_count',
            'other_participant'
        ]

    def get_last_message(self, obj):
        """Get the last message in the conversation"""
        last_message = obj.messages.filter(is_deleted=False).last()
        if last_message:
            return {
                'id': str(last_message.id),
                'content': last_message.content,
                'message_type': last_message.message_type,
                'sender_name': last_message.sender.name,
                'created_at': last_message.created_at.isoformat()
            }
        return None

    def get_unread_count(self, obj):
        """Get unread message count for current user"""
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            try:
                participant = obj.participant_settings.get(user=request.user)
                return participant.get_unread_count()
            except ConversationParticipant.DoesNotExist:
                return 0
        return 0

    def get_other_participant(self, obj):
        """Get the other participant in a direct conversation"""
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            other_user = obj.get_other_participant(request.user)
            if other_user:
                # Include presence information
                presence = getattr(other_user, 'presence', None)
                return {
                    'id': str(other_user.id),
                    'name': other_user.name,
                    'email': other_user.email,
                    'status': presence.status if presence else 'offline',
                    'last_seen': presence.last_seen.isoformat() if presence and presence.last_seen else None
                }
        return None


class ConversationCreateSerializer(serializers.Serializer):
    """Serializer for creating new conversations"""
    participant_id = serializers.UUIDField()
    conversation_type = serializers.ChoiceField(
        choices=Conversation.CONVERSATION_TYPES,
        default='direct'
    )

    def validate_participant_id(self, value):
        """Validate that the participant exists and is not the current user"""
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            if str(request.user.id) == str(value):
                raise serializers.ValidationError("Cannot create conversation with yourself")
            
            try:
                User.objects.get(id=value)
            except User.DoesNotExist:
                raise serializers.ValidationError("User does not exist")
        
        return value

    def create(self, validated_data):
        """Create a new conversation"""
        request = self.context.get('request')
        participant_id = validated_data['participant_id']
        conversation_type = validated_data['conversation_type']
        
        # Check if conversation already exists
        existing_conversation = Conversation.objects.filter(
            participants=request.user,
            conversation_type=conversation_type
        ).filter(
            participants__id=participant_id
        ).first()
        
        if existing_conversation:
            return existing_conversation
        
        # Create new conversation
        conversation = Conversation.objects.create(
            conversation_type=conversation_type
        )
        
        # Add participants
        conversation.participants.add(request.user)
        conversation.participants.add(participant_id)
        
        # Create participant settings
        ConversationParticipant.objects.create(
            conversation=conversation,
            user=request.user
        )
        ConversationParticipant.objects.create(
            conversation=conversation,
            user_id=participant_id
        )
        
        return conversation


class MessageReactionSerializer(serializers.Serializer):
    """Serializer for message reactions"""
    emoji = serializers.CharField(max_length=10)
    action = serializers.ChoiceField(choices=['add', 'remove'], default='add')

    def update(self, instance, validated_data):
        """Update message reactions"""
        request = self.context.get('request')
        emoji = validated_data['emoji']
        action = validated_data['action']
        
        if action == 'add':
            instance.add_reaction(request.user, emoji)
        elif action == 'remove':
            instance.remove_reaction(request.user)
        
        return instance
