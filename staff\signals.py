from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from django.contrib.auth import get_user_model
from django.utils import timezone
from decimal import Decimal
from .models import StaffProfile, Referral, Earnings

User = get_user_model()

@receiver(post_save, sender=User)
def create_staff_profile(sender, instance, created, **kwargs):
    """Create a StaffProfile when a staff user is created."""
    if created and instance.is_staff:
        StaffProfile.objects.create(
            user=instance,
            staff_type='support',  # Default type
            commission_rate=Decimal('10.00')  # Default 10% commission
        )

@receiver(post_save, sender=Referral)
def process_referral_conversion(sender, instance, created, **kwargs):
    """Process referral conversion and create earnings record."""
    if not created and instance.status == 'converted' and not instance.conversion_date:
        # Update referral
        instance.conversion_date = timezone.now()
        instance.save(update_fields=['conversion_date'])

        # Calculate commission based on referral type/value
        commission_amount = instance.commission_earned

        # Create earnings record
        if commission_amount > 0:
            Earnings.objects.create(
                staff=instance.staff,
                amount=commission_amount,
                source='referral',
                description=f'Commission for referral: {instance.referred_user.email}',
                status='pending'
            )

            # Update staff total earnings
            staff_profile = instance.staff
            staff_profile.total_earnings = staff_profile.calculate_earnings()
            staff_profile.save(update_fields=['total_earnings'])

@receiver(post_save, sender=Earnings)
def update_staff_total_earnings(sender, instance, created, **kwargs):
    """Update staff total earnings when a new earnings record is created."""
    if created or instance.status == 'paid':
        staff_profile = instance.staff
        staff_profile.total_earnings = staff_profile.calculate_earnings()
        staff_profile.save(update_fields=['total_earnings'])