"""
HeartGrid Gamification System

This module handles achievements, user stats, and gamification features
for the HeartGrid dating platform.
"""

from django.utils import timezone
from django.db.models import Count, Q
from .models import User, Profile, Like, Match, Message, Achievement, UserActivity


class GamificationEngine:
    """
    Handles achievement tracking and gamification logic
    """
    
    # Achievement definitions
    ACHIEVEMENTS = {
        'first_like': {
            'title': 'First Impression',
            'description': 'Send your first like',
            'icon': '👍',
            'points': 10
        },
        'first_match': {
            'title': 'Perfect Match',
            'description': 'Get your first match',
            'icon': '💕',
            'points': 50
        },
        'first_message': {
            'title': 'Conversation Starter',
            'description': 'Send your first message',
            'icon': '💬',
            'points': 25
        },
        'profile_complete': {
            'title': 'Profile Perfectionist',
            'description': 'Complete your profile with photos and bio',
            'icon': '✨',
            'points': 30
        },
        'early_adopter': {
            'title': 'Early Adopter',
            'description': 'Join <PERSON> in the first month',
            'icon': '🚀',
            'points': 100
        },
        'social_butterfly': {
            'title': 'Social Butterfly',
            'description': 'Send 50 likes',
            'icon': '🦋',
            'points': 75
        },
        'conversation_master': {
            'title': 'Conversation Master',
            'description': 'Send 100 messages',
            'icon': '🎯',
            'points': 100
        },
        'match_maker': {
            'title': 'Match Maker',
            'description': 'Get 10 matches',
            'icon': '💘',
            'points': 150
        },
        'popular': {
            'title': 'Popular',
            'description': 'Receive 25 likes',
            'icon': '⭐',
            'points': 80
        },
        'premium_member': {
            'title': 'Premium Member',
            'description': 'Subscribe to a premium plan',
            'icon': '👑',
            'points': 200
        }
    }
    
    def __init__(self):
        pass
    
    def check_and_award_achievements(self, user):
        """
        Check and award any new achievements for a user
        """
        awarded_achievements = []
        
        # Get user's existing achievements
        existing_achievements = set(
            Achievement.objects.filter(user=user).values_list('achievement_type', flat=True)
        )
        
        # Check each achievement
        for achievement_type, achievement_data in self.ACHIEVEMENTS.items():
            if achievement_type not in existing_achievements:
                if self._check_achievement_condition(user, achievement_type):
                    achievement = Achievement.objects.create(
                        user=user,
                        achievement_type=achievement_type,
                        title=achievement_data['title'],
                        description=achievement_data['description'],
                        icon=achievement_data['icon']
                    )
                    awarded_achievements.append(achievement)
        
        return awarded_achievements
    
    def _check_achievement_condition(self, user, achievement_type):
        """
        Check if user meets the condition for a specific achievement
        """
        if achievement_type == 'first_like':
            return Like.objects.filter(liker=user).exists()
        
        elif achievement_type == 'first_match':
            return Match.objects.filter(
                Q(user1=user) | Q(user2=user), is_active=True
            ).exists()
        
        elif achievement_type == 'first_message':
            return Message.objects.filter(sender=user).exists()
        
        elif achievement_type == 'profile_complete':
            try:
                profile = user.profile
                return profile.is_complete
            except Profile.DoesNotExist:
                return False
        
        elif achievement_type == 'early_adopter':
            # Check if user joined within first month of platform launch
            # For demo purposes, consider all users as early adopters
            return True
        
        elif achievement_type == 'social_butterfly':
            return Like.objects.filter(liker=user).count() >= 50
        
        elif achievement_type == 'conversation_master':
            return Message.objects.filter(sender=user).count() >= 100
        
        elif achievement_type == 'match_maker':
            return Match.objects.filter(
                Q(user1=user) | Q(user2=user), is_active=True
            ).count() >= 10
        
        elif achievement_type == 'popular':
            return Like.objects.filter(liked=user).count() >= 25
        
        elif achievement_type == 'premium_member':
            from .models import Subscription
            try:
                subscription = Subscription.objects.get(user=user)
                return subscription.is_active() and subscription.plan != 'trial'
            except Subscription.DoesNotExist:
                return False
        
        return False
    
    def get_user_stats(self, user):
        """
        Get comprehensive user statistics
        """
        # Basic stats
        likes_sent = Like.objects.filter(liker=user).count()
        likes_received = Like.objects.filter(liked=user).count()
        super_likes_sent = Like.objects.filter(liker=user, is_super_like=True).count()
        super_likes_received = Like.objects.filter(liked=user, is_super_like=True).count()
        
        # Match stats
        matches = Match.objects.filter(
            Q(user1=user) | Q(user2=user), is_active=True
        ).count()
        
        # Message stats
        messages_sent = Message.objects.filter(sender=user).count()
        messages_received = Message.objects.filter(receiver=user).count()
        
        # Profile stats
        profile_views = 0  # This would be tracked separately
        try:
            profile = user.profile
            profile_complete = profile.is_complete
            photos_count = profile.photos.count()
        except Profile.DoesNotExist:
            profile_complete = False
            photos_count = 0
        
        # Achievement stats
        achievements_count = Achievement.objects.filter(user=user).count()
        total_points = sum(
            self.ACHIEVEMENTS.get(achievement.achievement_type, {}).get('points', 0)
            for achievement in Achievement.objects.filter(user=user)
        )
        
        # Activity stats
        login_streak = self._calculate_login_streak(user)
        last_active = UserActivity.objects.filter(user=user).order_by('-created_at').first()
        
        return {
            'likes': {
                'sent': likes_sent,
                'received': likes_received,
                'super_likes_sent': super_likes_sent,
                'super_likes_received': super_likes_received
            },
            'matches': {
                'total': matches,
                'match_rate': round((matches / likes_sent * 100) if likes_sent > 0 else 0, 1)
            },
            'messages': {
                'sent': messages_sent,
                'received': messages_received,
                'total': messages_sent + messages_received
            },
            'profile': {
                'complete': profile_complete,
                'photos_count': photos_count,
                'views': profile_views
            },
            'achievements': {
                'count': achievements_count,
                'total_points': total_points,
                'completion_rate': round((achievements_count / len(self.ACHIEVEMENTS) * 100), 1)
            },
            'activity': {
                'login_streak': login_streak,
                'last_active': last_active.created_at if last_active else None,
                'member_since': user.created_at
            }
        }
    
    def _calculate_login_streak(self, user):
        """
        Calculate user's current login streak
        """
        from datetime import timedelta
        
        # Get recent login activities
        login_activities = UserActivity.objects.filter(
            user=user,
            activity_type='login'
        ).order_by('-created_at')
        
        if not login_activities.exists():
            return 0
        
        streak = 0
        current_date = timezone.now().date()
        
        for activity in login_activities:
            activity_date = activity.created_at.date()
            
            if activity_date == current_date or activity_date == current_date - timedelta(days=streak):
                streak += 1
                current_date = activity_date
            else:
                break
        
        return streak

    def check_achievements(self, user):
        """Check and unlock achievements for a user"""
        unlocked_achievements = []

        for achievement_key, achievement_data in self.ACHIEVEMENTS.items():
            # Check if user already has this achievement
            if Achievement.objects.filter(user=user, achievement_type=achievement_key).exists():
                continue

            # Check achievement conditions
            if self._check_achievement_condition(user, achievement_key):
                # Unlock achievement
                achievement = Achievement.objects.create(
                    user=user,
                    achievement_type=achievement_key,
                    title=achievement_data['title'],
                    description=achievement_data['description'],
                    icon=achievement_data['icon']
                )

                unlocked_achievements.append({
                    'id': str(achievement.id),
                    'type': achievement_key,
                    'title': achievement_data['title'],
                    'points': achievement_data['points']
                })

        return unlocked_achievements

    def get_leaderboard(self, limit=10):
        """
        Get top users by achievement points
        """
        users_with_points = []
        
        for user in User.objects.filter(is_active=True)[:50]:  # Limit for performance
            achievements = Achievement.objects.filter(user=user)
            total_points = sum(
                self.ACHIEVEMENTS.get(achievement.achievement_type, {}).get('points', 0)
                for achievement in achievements
            )
            
            if total_points > 0:
                users_with_points.append({
                    'user': user,
                    'points': total_points,
                    'achievements_count': achievements.count()
                })
        
        # Sort by points and return top users
        users_with_points.sort(key=lambda x: x['points'], reverse=True)
        return users_with_points[:limit]
    
    def trigger_achievement_check(self, user, activity_type):
        """
        Trigger achievement check after specific user activities
        """
        # Map activities to potential achievements
        activity_achievements = {
            'like_sent': ['first_like', 'social_butterfly'],
            'match_created': ['first_match', 'match_maker'],
            'message_sent': ['first_message', 'conversation_master'],
            'profile_updated': ['profile_complete'],
            'subscription_created': ['premium_member']
        }
        
        relevant_achievements = activity_achievements.get(activity_type, [])
        awarded = []
        
        for achievement_type in relevant_achievements:
            if not Achievement.objects.filter(user=user, achievement_type=achievement_type).exists():
                if self._check_achievement_condition(user, achievement_type):
                    achievement_data = self.ACHIEVEMENTS[achievement_type]
                    achievement = Achievement.objects.create(
                        user=user,
                        achievement_type=achievement_type,
                        title=achievement_data['title'],
                        description=achievement_data['description'],
                        icon=achievement_data['icon']
                    )
                    awarded.append(achievement)
        
        return awarded


# Global instance
gamification_engine = GamificationEngine()
