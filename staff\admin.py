from django.contrib import admin
from .models import StaffProfile, Referral, Earnings

@admin.register(StaffProfile)
class StaffProfileAdmin(admin.ModelAdmin):
    list_display = ('user', 'staff_type', 'referral_code', 'total_earnings', 'is_active', 'date_joined')
    list_filter = ('staff_type', 'is_active', 'date_joined')
    search_fields = ('user__email', 'user__first_name', 'user__last_name', 'referral_code')
    readonly_fields = ('referral_code', 'date_joined')
    ordering = ('-date_joined',)

@admin.register(Referral)
class ReferralAdmin(admin.ModelAdmin):
    list_display = ('staff', 'referred_user', 'date_referred', 'status', 'commission_earned')
    list_filter = ('status', 'date_referred')
    search_fields = ('staff__user__email', 'referred_user__email')
    readonly_fields = ('date_referred',)
    ordering = ('-date_referred',)

@admin.register(Earnings)
class EarningsAdmin(admin.ModelAdmin):
    list_display = ('staff', 'amount', 'source', 'status', 'date')
    list_filter = ('source', 'status', 'date')
    search_fields = ('staff__user__email', 'transaction_id', 'description')
    readonly_fields = ('date',)
    ordering = ('-date',)
