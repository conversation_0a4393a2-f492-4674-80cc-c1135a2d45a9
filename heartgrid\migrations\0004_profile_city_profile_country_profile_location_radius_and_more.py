# Generated by Django 5.2.3 on 2025-06-28 04:09

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('heartgrid', '0003_alter_user_managers'),
    ]

    operations = [
        migrations.AddField(
            model_name='profile',
            name='city',
            field=models.CharField(blank=True, max_length=100),
        ),
        migrations.AddField(
            model_name='profile',
            name='country',
            field=models.CharField(blank=True, choices=[('US', 'United States'), ('CA', 'Canada'), ('GB', 'United Kingdom'), ('AU', 'Australia'), ('DE', 'Germany'), ('FR', 'France'), ('IT', 'Italy'), ('ES', 'Spain'), ('NL', 'Netherlands'), ('SE', 'Sweden'), ('NO', 'Norway'), ('DK', 'Denmark'), ('FI', 'Finland'), ('CH', 'Switzerland'), ('AT', 'Austria'), ('BE', 'Belgium'), ('IE', 'Ireland'), ('PT', 'Portugal'), ('GR', 'Greece'), ('PL', 'Poland'), ('CZ', 'Czech Republic'), ('HU', 'Hungary'), ('RO', 'Romania'), ('BG', 'Bulgaria'), ('HR', 'Croatia'), ('SI', 'Slovenia'), ('SK', 'Slovakia'), ('LT', 'Lithuania'), ('LV', 'Latvia'), ('EE', 'Estonia'), ('JP', 'Japan'), ('KR', 'South Korea'), ('CN', 'China'), ('IN', 'India'), ('SG', 'Singapore'), ('HK', 'Hong Kong'), ('TW', 'Taiwan'), ('TH', 'Thailand'), ('MY', 'Malaysia'), ('PH', 'Philippines'), ('ID', 'Indonesia'), ('VN', 'Vietnam'), ('NZ', 'New Zealand'), ('ZA', 'South Africa'), ('BR', 'Brazil'), ('MX', 'Mexico'), ('AR', 'Argentina'), ('CL', 'Chile'), ('CO', 'Colombia'), ('PE', 'Peru'), ('UY', 'Uruguay'), ('EC', 'Ecuador'), ('VE', 'Venezuela'), ('OTHER', 'Other')], max_length=10),
        ),
        migrations.AddField(
            model_name='profile',
            name='location_radius',
            field=models.PositiveIntegerField(default=50, help_text='Preferred location radius in kilometers'),
        ),
        migrations.AddField(
            model_name='profile',
            name='max_age_preference',
            field=models.PositiveIntegerField(blank=True, help_text='Maximum age preference for matches', null=True),
        ),
        migrations.AddField(
            model_name='profile',
            name='min_age_preference',
            field=models.PositiveIntegerField(blank=True, help_text='Minimum age preference for matches', null=True),
        ),
        migrations.AddField(
            model_name='profile',
            name='preferred_genders',
            field=models.JSONField(blank=True, default=list, help_text='List of preferred genders for matching'),
        ),
    ]
