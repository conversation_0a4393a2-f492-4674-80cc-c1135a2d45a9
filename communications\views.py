"""
API Views for HeartGrid Communications

This module contains DRF API views for messaging, conversations, and presence.
"""

from rest_framework import generics, status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.pagination import PageNumberPagination
from django.contrib.auth import get_user_model
from django.db.models import Q, Prefetch
from django.utils import timezone
from .models import (
    Conversation, Message, UserPresence, MessageStatus,
    ConversationParticipant
)
from .serializers import (
    ConversationSerializer, ConversationCreateSerializer,
    MessageSerializer, UserPresenceSerializer,
    MessageReactionSerializer
)

User = get_user_model()


class MessagePagination(PageNumberPagination):
    """Custom pagination for messages"""
    page_size = 50
    page_size_query_param = 'page_size'
    max_page_size = 100


class ConversationListCreateView(generics.ListCreateAPIView):
    """
    List user's conversations or create a new conversation
    """
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return ConversationCreateSerializer
        return ConversationSerializer

    def get_queryset(self):
        """Get conversations for the current user"""
        return Conversation.objects.filter(
            participants=self.request.user,
            is_active=True
        ).prefetch_related(
            'participants',
            'participant_settings',
            'messages'
        ).order_by('-last_message_at', '-updated_at')

    def perform_create(self, serializer):
        """Create a new conversation"""
        serializer.save()


class ConversationDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    Retrieve, update, or delete a conversation
    """
    serializer_class = ConversationSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        """Get conversations for the current user"""
        return Conversation.objects.filter(
            participants=self.request.user
        ).prefetch_related('participants', 'participant_settings')

    def perform_destroy(self, instance):
        """Soft delete conversation (archive it)"""
        # Archive conversation for current user
        try:
            participant = instance.participant_settings.get(user=self.request.user)
            participant.is_archived = True
            participant.save()
        except ConversationParticipant.DoesNotExist:
            pass


class MessageListCreateView(generics.ListCreateAPIView):
    """
    List messages in a conversation or create a new message
    """
    serializer_class = MessageSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = MessagePagination

    def get_queryset(self):
        """Get messages for a specific conversation"""
        conversation_id = self.kwargs.get('conversation_id')

        # Verify user is participant in conversation
        try:
            conversation = Conversation.objects.get(
                id=conversation_id,
                participants=self.request.user
            )
        except Conversation.DoesNotExist:
            return Message.objects.none()

        return Message.objects.filter(
            conversation=conversation,
            is_deleted=False
        ).select_related('sender').prefetch_related('statuses').order_by('-created_at')

    def perform_create(self, serializer):
        """Create a new message"""
        conversation_id = self.kwargs.get('conversation_id')

        # Verify user is participant in conversation
        try:
            conversation = Conversation.objects.get(
                id=conversation_id,
                participants=self.request.user
            )
        except Conversation.DoesNotExist:
            return Response(
                {'error': 'Conversation not found'},
                status=status.HTTP_404_NOT_FOUND
            )

        # Create message
        message = serializer.save(conversation=conversation)

        # Create message status for other participants
        participants = conversation.participants.exclude(id=self.request.user.id)
        for participant in participants:
            MessageStatus.objects.create(
                message=message,
                user=participant,
                status='sent'
            )


class MessageDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    Retrieve, update, or delete a message
    """
    serializer_class = MessageSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        """Get messages that the user can access"""
        return Message.objects.filter(
            conversation__participants=self.request.user,
            is_deleted=False
        ).select_related('sender', 'conversation')

    def perform_update(self, serializer):
        """Update message (only sender can edit)"""
        message = self.get_object()
        if message.sender != self.request.user:
            return Response(
                {'error': 'You can only edit your own messages'},
                status=status.HTTP_403_FORBIDDEN
            )

        serializer.save(edited_at=timezone.now())

    def perform_destroy(self, instance):
        """Soft delete message (only sender can delete)"""
        if instance.sender != self.request.user:
            return Response(
                {'error': 'You can only delete your own messages'},
                status=status.HTTP_403_FORBIDDEN
            )

        instance.soft_delete()


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def mark_messages_read(request, conversation_id):
    """
    Mark messages as read in a conversation
    """
    try:
        conversation = Conversation.objects.get(
            id=conversation_id,
            participants=request.user
        )
    except Conversation.DoesNotExist:
        return Response(
            {'error': 'Conversation not found'},
            status=status.HTTP_404_NOT_FOUND
        )

    # Get the latest message ID from request or use latest message
    message_id = request.data.get('message_id')

    if message_id:
        try:
            message = Message.objects.get(
                id=message_id,
                conversation=conversation,
                is_deleted=False
            )
        except Message.DoesNotExist:
            return Response(
                {'error': 'Message not found'},
                status=status.HTTP_404_NOT_FOUND
            )
    else:
        # Mark all messages as read
        message = conversation.messages.filter(is_deleted=False).last()

    if message:
        # Update participant's last read message
        participant, created = ConversationParticipant.objects.get_or_create(
            conversation=conversation,
            user=request.user
        )
        participant.mark_as_read(message)

        # Update message statuses
        MessageStatus.objects.filter(
            message__conversation=conversation,
            message__created_at__lte=message.created_at,
            user=request.user,
            status__in=['sent', 'delivered']
        ).update(status='read', read_at=timezone.now())

    return Response({'success': True})


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def message_reaction(request, message_id):
    """
    Add or remove reaction to a message
    """
    try:
        message = Message.objects.get(
            id=message_id,
            conversation__participants=request.user,
            is_deleted=False
        )
    except Message.DoesNotExist:
        return Response(
            {'error': 'Message not found'},
            status=status.HTTP_404_NOT_FOUND
        )

    serializer = MessageReactionSerializer(
        message,
        data=request.data,
        context={'request': request}
    )

    if serializer.is_valid():
        serializer.save()
        return Response({
            'message_id': str(message.id),
            'reactions': message.reactions
        })

    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class UserPresenceView(generics.RetrieveUpdateAPIView):
    """
    Get or update user presence information
    """
    serializer_class = UserPresenceSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_object(self):
        """Get or create user presence"""
        presence, created = UserPresence.objects.get_or_create(
            user=self.request.user
        )
        return presence


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def get_user_presence(request, user_id):
    """
    Get presence information for a specific user
    """
    try:
        user = User.objects.get(id=user_id)
        presence = getattr(user, 'presence', None)

        if presence:
            serializer = UserPresenceSerializer(presence)
            return Response(serializer.data)
        else:
            return Response({
                'user': {'id': str(user.id), 'name': user.name, 'email': user.email},
                'status': 'offline',
                'last_seen': None,
                'last_activity': None
            })
    except User.DoesNotExist:
        return Response(
            {'error': 'User not found'},
            status=status.HTTP_404_NOT_FOUND
        )


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def update_typing_status(request):
    """
    Update typing status for current user
    """
    is_typing = request.data.get('is_typing', False)
    to_user_id = request.data.get('to_user_id')

    try:
        presence, created = UserPresence.objects.get_or_create(user=request.user)

        if is_typing and to_user_id:
            try:
                to_user = User.objects.get(id=to_user_id)
                presence.set_typing(to_user)
            except User.DoesNotExist:
                return Response(
                    {'error': 'Target user not found'},
                    status=status.HTTP_404_NOT_FOUND
                )
        else:
            presence.clear_typing()

        return Response({'success': True})
    except Exception as e:
        return Response(
            {'error': str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def test_communications(request):
    """
    Test endpoint for communications functionality
    """
    from .utils import get_user_status, get_recent_conversations

    user_status = get_user_status(request.user)
    recent_conversations = get_recent_conversations(request.user, limit=5)

    return Response({
        'user': {
            'id': str(request.user.id),
            'name': request.user.name,
            'email': request.user.email
        },
        'status': user_status,
        'recent_conversations_count': recent_conversations.count(),
        'websocket_urls': {
            'presence': '/ws/presence/',
            'notifications': '/ws/notifications/',
            'chat_example': '/ws/chat/{conversation_id}/'
        },
        'api_endpoints': {
            'conversations': '/api/communications/conversations/',
            'presence': '/api/communications/presence/',
            'typing': '/api/communications/presence/typing/'
        }
    })
