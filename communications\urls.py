"""
URL patterns for HeartGrid Communications

This module defines URL patterns for the communications API endpoints.
"""

from django.urls import path
from . import views

app_name = 'communications'

urlpatterns = [
    # Conversation endpoints
    path('conversations/', views.ConversationListCreateView.as_view(), name='conversation-list-create'),
    path('conversations/<uuid:pk>/', views.ConversationDetailView.as_view(), name='conversation-detail'),
    
    # Message endpoints
    path('conversations/<uuid:conversation_id>/messages/', views.MessageListCreateView.as_view(), name='message-list-create'),
    path('messages/<uuid:pk>/', views.MessageDetailView.as_view(), name='message-detail'),
    path('conversations/<uuid:conversation_id>/mark-read/', views.mark_messages_read, name='mark-messages-read'),
    path('messages/<uuid:message_id>/reaction/', views.message_reaction, name='message-reaction'),
    
    # Presence endpoints
    path('presence/', views.UserPresenceView.as_view(), name='user-presence'),
    path('presence/<uuid:user_id>/', views.get_user_presence, name='get-user-presence'),
    path('presence/typing/', views.update_typing_status, name='update-typing-status'),

    # Test endpoint
    path('test/', views.test_communications, name='test-communications'),
]
