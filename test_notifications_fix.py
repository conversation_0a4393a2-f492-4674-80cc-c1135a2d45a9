#!/usr/bin/env python
"""
Test script specifically for the notifications QuerySet slice issue
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'heartgrid_django.settings')
django.setup()

from django.test import TestCase, Client
from django.contrib.auth import get_user_model
from heartgrid.models import User, Notification
from django.utils import timezone

User = get_user_model()

def test_notifications_queryset():
    """Test that notifications QuerySet operations work correctly"""
    print("🧪 Testing notifications QuerySet operations...")
    
    try:
        # Create test user
        user = User.objects.create_user(
            username='notificationtest',
            email='<EMAIL>',
            password='testpass123',
            name='Notification Test'
        )
        
        # Create test notifications
        notifications_data = [
            {'title': 'Welcome!', 'message': 'Welcome to HeartGrid!', 'is_read': False},
            {'title': 'New Match!', 'message': 'You have a new match!', 'is_read': False},
            {'title': 'Message Received', 'message': 'You received a new message', 'is_read': True},
            {'title': 'Achievement Unlocked', 'message': 'You unlocked a new achievement!', 'is_read': False},
            {'title': 'Daily Challenge', 'message': 'Complete your daily challenge', 'is_read': True},
        ]

        created_notifications = []
        for data in notifications_data:
            notification = Notification.objects.create(
                user=user,
                title=data['title'],
                message=data['message'],
                notification_type='system',
                is_read=data['is_read']
            )
            created_notifications.append(notification)
        
        print(f"  ✅ Created {len(created_notifications)} test notifications")
        
        # Test the original problematic approach (should fail)
        try:
            notifications_slice = Notification.objects.filter(user=user).order_by('-created_at')[:3]
            unread_count_bad = notifications_slice.filter(is_read=False).count()  # This should fail
            print("  ❌ Original approach should have failed but didn't")
            return False
        except TypeError as e:
            if "Cannot filter a query once a slice has been taken" in str(e):
                print("  ✅ Original approach correctly fails with expected error")
            else:
                print(f"  ❌ Original approach failed with unexpected error: {e}")
                return False
        
        # Test the fixed approach (should work)
        try:
            user_notifications = Notification.objects.filter(user=user).order_by('-created_at')
            unread_count = user_notifications.filter(is_read=False).count()
            notifications = user_notifications[:3]

            print(f"  ✅ Fixed approach works: {unread_count} unread notifications")
            print(f"  ✅ Retrieved {len(list(notifications))} notifications for display")

            # Verify the counts are correct
            expected_unread = sum(1 for n in notifications_data if not n['is_read'])
            if unread_count == expected_unread:
                print(f"  ✅ Unread count is correct: {unread_count}")
            else:
                print(f"  ❌ Unread count mismatch: expected {expected_unread}, got {unread_count}")
                return False
                
        except Exception as e:
            print(f"  ❌ Fixed approach failed: {e}")
            return False
        
        # Clean up
        user.delete()
        print("  ✅ Test data cleaned up")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Notifications QuerySet test error: {e}")
        return False

def test_notifications_view():
    """Test the notifications view directly"""
    print("\n🧪 Testing notifications view...")
    
    try:
        client = Client()
        
        # Create test user
        user = User.objects.create_user(
            username='notificationviewtest',
            email='<EMAIL>',
            password='testpass123',
            name='Notification View Test'
        )
        
        # Create test notifications
        for i in range(10):
            Notification.objects.create(
                user=user,
                title=f'Test Notification {i+1}',
                message=f'This is test notification number {i+1}',
                notification_type='system',
                is_read=(i % 3 == 0)  # Every 3rd notification is read
            )
        
        # Login user
        client.login(username='notificationviewtest', password='testpass123')
        
        # Test notifications page
        response = client.get('/notifications/')
        
        if response.status_code == 200:
            print("  ✅ Notifications page returns 200 OK")
            
            # Check if context contains expected data
            if 'notifications' in response.context:
                notifications = response.context['notifications']
                print(f"  ✅ Context contains notifications: {len(notifications)} items")
            else:
                print("  ❌ Context missing 'notifications' key")
                return False
                
            if 'unread_count' in response.context:
                unread_count = response.context['unread_count']
                print(f"  ✅ Context contains unread_count: {unread_count}")
            else:
                print("  ❌ Context missing 'unread_count' key")
                return False
                
        else:
            print(f"  ❌ Notifications page returns {response.status_code}")
            if hasattr(response, 'content'):
                print(f"  Error content: {response.content.decode()[:200]}...")
            return False
        
        # Clean up
        user.delete()
        print("  ✅ Notifications view test data cleaned up")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Notifications view test error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all notifications tests"""
    print("🚀 HeartGrid Notifications Fix Test Suite")
    print("=" * 50)
    
    tests = [
        test_notifications_queryset,
        test_notifications_view
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
            failed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All notifications tests passed! QuerySet slice fix is working correctly.")
    else:
        print("⚠️  Some notifications tests failed. Please check the errors above.")
    
    return failed == 0

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
