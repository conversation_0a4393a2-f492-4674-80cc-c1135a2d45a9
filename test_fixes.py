#!/usr/bin/env python
"""
Test script to verify fixes for gamification, notifications, and subscription templates
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'heartgrid_django.settings')
django.setup()

from django.test import TestCase, Client
from django.contrib.auth import get_user_model
from django.urls import reverse
from heartgrid.models import User, Profile, Subscription, Achievement, UserActivity, Notification
from heartgrid.gamification import gamification_engine
from django.utils import timezone
from datetime import timedelta

User = get_user_model()

def test_imports():
    """Test that all imports work correctly"""
    print("🧪 Testing imports...")
    
    try:
        # Test Django settings
        from django.conf import settings
        print("  ✅ Django settings imported")
        
        # Test OAuth2 provider
        import oauth2_provider
        print("  ✅ OAuth2 provider imported")
        
        # Test communications app
        import communications
        from communications.models import UserPresence, Conversation
        print("  ✅ Communications app imported")
        
        # Test gamification
        from heartgrid.gamification import GamificationEngine
        print("  ✅ Gamification engine imported")
        
        # Test models
        from heartgrid.models import Achievement, UserActivity, Subscription
        print("  ✅ All models imported")
        
        return True
        
    except ImportError as e:
        print(f"  ❌ Import error: {e}")
        return False

def test_model_functionality():
    """Test that models work correctly"""
    print("\n🧪 Testing model functionality...")
    
    try:
        # Create test user
        user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            name='Test User'
        )
        print("  ✅ User created successfully")
        
        # Test subscription model
        subscription = Subscription.objects.create(
            user=user,
            plan='weekly',
            status='active',
            features=['basic_chat', 'super_likes'],
            expires_at=timezone.now() + timedelta(days=7)
        )
        
        # Test is_active method
        is_active = subscription.is_active()
        print(f"  ✅ Subscription is_active method works: {is_active}")
        
        # Test achievement model
        achievement = Achievement.objects.create(
            user=user,
            achievement_type='first_like',
            title='First Impression',
            description='Send your first like',
            icon='👍'
        )
        print("  ✅ Achievement created successfully")
        
        # Test user activity model
        activity = UserActivity.objects.create(
            user=user,
            activity_type='login',
            metadata={'ip_address': '127.0.0.1'}
        )
        print("  ✅ UserActivity created successfully")
        
        # Test notification model
        notification = Notification.objects.create(
            user=user,
            title='Welcome!',
            message='Welcome to HeartGrid!',
            type='system'
        )
        print("  ✅ Notification created successfully")
        
        # Clean up
        user.delete()
        print("  ✅ Test data cleaned up")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Model test error: {e}")
        return False

def test_gamification_engine():
    """Test gamification engine functionality"""
    print("\n🧪 Testing gamification engine...")
    
    try:
        # Create test user
        user = User.objects.create_user(
            username='gamificationtest',
            email='<EMAIL>',
            password='testpass123',
            name='Gamification Test'
        )
        
        # Test get_user_stats
        stats = gamification_engine.get_user_stats(user)
        print(f"  ✅ User stats retrieved: Level {stats.get('level', 0)}")
        
        # Test achievement checking
        achievements = gamification_engine.check_and_award_achievements(user)
        print(f"  ✅ Achievement check completed: {len(achievements)} achievements")
        
        # Test leaderboard
        leaderboard = gamification_engine.get_leaderboard()
        print(f"  ✅ Leaderboard retrieved: {len(leaderboard)} users")
        
        # Clean up
        user.delete()
        print("  ✅ Gamification test data cleaned up")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Gamification test error: {e}")
        return False

def test_view_functionality():
    """Test that views work correctly"""
    print("\n🧪 Testing view functionality...")
    
    try:
        client = Client()
        
        # Create test user
        user = User.objects.create_user(
            username='viewtest',
            email='<EMAIL>',
            password='testpass123',
            name='View Test'
        )
        
        # Login user
        client.login(username='viewtest', password='testpass123')
        
        # Test gamification page
        response = client.get('/gamification/')
        print(f"  ✅ Gamification page status: {response.status_code}")
        
        # Test subscription page
        response = client.get('/subscription/')
        print(f"  ✅ Subscription page status: {response.status_code}")
        
        # Test notifications page
        response = client.get('/notifications/')
        print(f"  ✅ Notifications page status: {response.status_code}")
        
        # Clean up
        user.delete()
        print("  ✅ View test data cleaned up")
        
        return True
        
    except Exception as e:
        print(f"  ❌ View test error: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 HeartGrid Fixes Test Suite")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_model_functionality,
        test_gamification_engine,
        test_view_functionality
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
            failed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All tests passed! Fixes are working correctly.")
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
    
    return failed == 0

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
