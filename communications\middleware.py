"""
Middleware for HeartGrid Communications

This module contains middleware for tracking user activity and presence.
"""

from django.utils import timezone
from django.utils.deprecation import MiddlewareMixin
from .models import UserPresence


class UserActivityMiddleware(MiddlewareMixin):
    """
    Middleware to track user activity for presence system
    """
    
    def process_request(self, request):
        """Update user's last activity on each request"""
        if request.user.is_authenticated:
            # Update user presence activity
            try:
                presence, created = UserPresence.objects.get_or_create(
                    user=request.user
                )
                presence.last_activity = timezone.now()
                
                # Set user as online if they were offline
                if presence.status == 'offline':
                    presence.status = 'online'
                
                presence.save(update_fields=['last_activity', 'status'])
                
            except Exception:
                # Silently fail to avoid breaking the request
                pass
        
        return None


class WebSocketAuthMiddleware:
    """
    Custom middleware for WebSocket authentication
    """
    
    def __init__(self, inner):
        self.inner = inner

    async def __call__(self, scope, receive, send):
        # Add any WebSocket-specific authentication logic here
        return await self.inner(scope, receive, send)
