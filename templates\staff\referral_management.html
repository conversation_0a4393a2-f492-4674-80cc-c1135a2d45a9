{% extends 'base.html' %}
{% load static %}

{% block title %}Referral Management{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Referral Management</h1>
    </div>

    <!-- Referral Code Card -->
    <div class="row">
        <div class="col-xl-6 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Your Referral Code</h6>
                </div>
                <div class="card-body">
                    <div class="input-group mb-3">
                        <input type="text" class="form-control" value="{{ referral_code }}" readonly>
                        <div class="input-group-append">
                            <button class="btn btn-outline-secondary" type="button" onclick="copyReferralCode()">
                                <i class="fas fa-copy"></i> Copy Code
                            </button>
                        </div>
                    </div>
                    <div class="input-group mb-3">
                        <input type="text" class="form-control" 
                               value="https://{{ request.get_host }}/register/?ref={{ referral_code }}" 
                               readonly>
                        <div class="input-group-append">
                            <button class="btn btn-outline-secondary" type="button" onclick="copyReferralLink()">
                                <i class="fas fa-copy"></i> Copy Link
                            </button>
                        </div>
                    </div>
                    <div class="mt-3">
                        <h6 class="font-weight-bold">Share your referral link:</h6>
                        <div class="social-share-buttons mt-2">
                            <a href="#" class="btn btn-primary btn-circle" onclick="shareOnFacebook()">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <a href="#" class="btn btn-info btn-circle ml-2" onclick="shareOnTwitter()">
                                <i class="fab fa-twitter"></i>
                            </a>
                            <a href="#" class="btn btn-success btn-circle ml-2" onclick="shareOnWhatsApp()">
                                <i class="fab fa-whatsapp"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Referral Statistics -->
        <div class="col-xl-6 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Referral Statistics</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-4">
                            <div class="card border-left-primary shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                                Total Referrals</div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                {{ referrals.count }}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-4">
                            <div class="card border-left-success shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                                Converted</div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                {{ referrals|filter_status:"converted"|length }}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-4">
                            <div class="card border-left-warning shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                                Pending</div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                {{ referrals|filter_status:"pending"|length }}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Referrals Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Your Referrals</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="referralsTable">
                    <thead>
                        <tr>
                            <th>User</th>
                            <th>Date Referred</th>
                            <th>Status</th>
                            <th>Commission</th>
                            <th>Conversion Date</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for referral in referrals %}
                        <tr>
                            <td>{{ referral.referred_user.get_full_name }}</td>
                            <td>{{ referral.date_referred|date:"M d, Y" }}</td>
                            <td>
                                <span class="badge badge-{{ referral.status }}">
                                    {{ referral.get_status_display }}
                                </span>
                            </td>
                            <td>${{ referral.commission_earned }}</td>
                            <td>
                                {% if referral.conversion_date %}
                                    {{ referral.conversion_date|date:"M d, Y" }}
                                {% else %}
                                    -
                                {% endif %}
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="5" class="text-center">No referrals yet</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    $('#referralsTable').DataTable({
        "order": [[1, "desc"]],
        "pageLength": 25
    });
});

function copyReferralCode() {
    const code = "{{ referral_code }}";
    navigator.clipboard.writeText(code).then(() => {
        alert('Referral code copied to clipboard!');
    });
}

function copyReferralLink() {
    const link = "https://{{ request.get_host }}/register/?ref={{ referral_code }}";
    navigator.clipboard.writeText(link).then(() => {
        alert('Referral link copied to clipboard!');
    });
}

function shareOnFacebook() {
    const link = encodeURIComponent("https://{{ request.get_host }}/register/?ref={{ referral_code }}");
    window.open(`https://www.facebook.com/sharer/sharer.php?u=${link}`, '_blank');
}

function shareOnTwitter() {
    const link = encodeURIComponent("https://{{ request.get_host }}/register/?ref={{ referral_code }}");
    const text = encodeURIComponent("Join me on HeartGrid! Use my referral link:");
    window.open(`https://twitter.com/intent/tweet?text=${text}&url=${link}`, '_blank');
}

function shareOnWhatsApp() {
    const link = encodeURIComponent("https://{{ request.get_host }}/register/?ref={{ referral_code }}");
    const text = encodeURIComponent("Join me on HeartGrid! Use my referral link: ");
    window.open(`https://wa.me/?text=${text}${link}`, '_blank');
}
</script>
{% endblock %}