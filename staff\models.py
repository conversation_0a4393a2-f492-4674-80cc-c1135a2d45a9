from django.db import models
from django.conf import settings
from django.utils.crypto import get_random_string
from decimal import Decimal

class StaffProfile(models.Model):
    user = models.OneToOneField(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    staff_type = models.CharField(max_length=50, choices=[
        ('support', 'Support Staff'),
        ('moderator', 'Moderator'),
        ('admin', 'Administrator')
    ])
    referral_code = models.CharField(max_length=20, unique=True)
    crypto_wallet_address = models.CharField(max_length=100, blank=True)
    total_earnings = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    commission_rate = models.DecimalField(max_digits=4, decimal_places=2, default=10)  # Percentage
    date_joined = models.DateTimeField(auto_now_add=True)
    is_active = models.BooleanField(default=True)

    def save(self, *args, **kwargs):
        if not self.referral_code:
            self.referral_code = get_random_string(10).upper()
        super().save(*args, **kwargs)

    def calculate_earnings(self, start_date=None, end_date=None):
        earnings_query = self.earnings_set
        if start_date:
            earnings_query = earnings_query.filter(date__gte=start_date)
        if end_date:
            earnings_query = earnings_query.filter(date__lte=end_date)
        return earnings_query.aggregate(total=models.Sum('amount'))['total'] or Decimal('0.00')

class Referral(models.Model):
    staff = models.ForeignKey(StaffProfile, on_delete=models.CASCADE)
    referred_user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    date_referred = models.DateTimeField(auto_now_add=True)
    status = models.CharField(max_length=20, choices=[
        ('pending', 'Pending'),
        ('active', 'Active'),
        ('converted', 'Converted')
    ])
    conversion_date = models.DateTimeField(null=True, blank=True)
    commission_earned = models.DecimalField(max_digits=10, decimal_places=2, default=0)

    class Meta:
        unique_together = ('staff', 'referred_user')

class Earnings(models.Model):
    staff = models.ForeignKey(StaffProfile, on_delete=models.CASCADE)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    date = models.DateTimeField(auto_now_add=True)
    source = models.CharField(max_length=50, choices=[
        ('referral', 'Referral Commission'),
        ('bonus', 'Performance Bonus'),
        ('salary', 'Base Salary')
    ])
    description = models.TextField(blank=True)
    status = models.CharField(max_length=20, choices=[
        ('pending', 'Pending'),
        ('processed', 'Processed'),
        ('paid', 'Paid')
    ], default='pending')
    transaction_id = models.CharField(max_length=100, blank=True)

    class Meta:
        ordering = ['-date']
