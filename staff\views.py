from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib.admin.views.decorators import staff_member_required
from django.utils import timezone
from django.db.models import Sum, Count
from django.http import JsonResponse
from datetime import datetime, timedelta
from decimal import Decimal

from .models import StaffProfile, Referral, Earnings

@login_required
def staff_dashboard(request):
    try:
        staff_profile = request.user.staffprofile
    except StaffProfile.DoesNotExist:
        return redirect('home')  # Redirect non-staff users

    # Calculate date ranges
    today = timezone.now()
    start_of_month = today.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
    
    # Get earnings statistics
    monthly_earnings = staff_profile.calculate_earnings(start_date=start_of_month)
    total_earnings = staff_profile.total_earnings
    
    # Get referral statistics
    referrals = Referral.objects.filter(staff=staff_profile)
    active_referrals = referrals.filter(status='active').count()
    converted_referrals = referrals.filter(status='converted').count()
    pending_referrals = referrals.filter(status='pending').count()
    
    # Get recent earnings
    recent_earnings = Earnings.objects.filter(staff=staff_profile).order_by('-date')[:5]
    
    context = {
        'staff_profile': staff_profile,
        'monthly_earnings': monthly_earnings,
        'total_earnings': total_earnings,
        'active_referrals': active_referrals,
        'converted_referrals': converted_referrals,
        'pending_referrals': pending_referrals,
        'recent_earnings': recent_earnings,
    }
    
    return render(request, 'staff/dashboard.html', context)

@staff_member_required
def admin_dashboard(request):
    # Get overall statistics
    total_staff = StaffProfile.objects.filter(is_active=True).count()
    total_referrals = Referral.objects.count()
    total_earnings_paid = Earnings.objects.filter(status='paid').aggregate(
        total=Sum('amount'))['total'] or Decimal('0.00')
    
    # Get top performing staff
    top_staff = StaffProfile.objects.annotate(
        referral_count=Count('referral'),
        total_commissions=Sum('earnings__amount')
    ).order_by('-total_commissions')[:5]
    
    # Get recent earnings transactions
    recent_transactions = Earnings.objects.select_related('staff__user').order_by('-date')[:10]
    
    context = {
        'total_staff': total_staff,
        'total_referrals': total_referrals,
        'total_earnings_paid': total_earnings_paid,
        'top_staff': top_staff,
        'recent_transactions': recent_transactions,
    }
    
    return render(request, 'staff/admin_dashboard.html', context)

@login_required
def earnings_history(request):
    try:
        staff_profile = request.user.staffprofile
    except StaffProfile.DoesNotExist:
        return redirect('home')

    earnings = Earnings.objects.filter(staff=staff_profile).order_by('-date')
    
    # Filter by date range if provided
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')
    
    if start_date:
        start_date = datetime.strptime(start_date, '%Y-%m-%d')
        earnings = earnings.filter(date__gte=start_date)
    if end_date:
        end_date = datetime.strptime(end_date, '%Y-%m-%d')
        earnings = earnings.filter(date__lte=end_date)
    
    context = {
        'earnings': earnings,
        'start_date': start_date,
        'end_date': end_date,
    }
    
    return render(request, 'staff/earnings_history.html', context)

@login_required
def referral_management(request):
    try:
        staff_profile = request.user.staffprofile
    except StaffProfile.DoesNotExist:
        return redirect('home')
    
    referrals = Referral.objects.filter(staff=staff_profile).order_by('-date_referred')
    
    context = {
        'referrals': referrals,
        'referral_code': staff_profile.referral_code,
    }
    
    return render(request, 'staff/referral_management.html', context)

@login_required
def update_wallet_address(request):
    if request.method != 'POST':
        return JsonResponse({'error': 'Invalid request method'}, status=400)
    
    try:
        staff_profile = request.user.staffprofile
    except StaffProfile.DoesNotExist:
        return JsonResponse({'error': 'Staff profile not found'}, status=404)
    
    wallet_address = request.POST.get('wallet_address')
    if not wallet_address:
        return JsonResponse({'error': 'Wallet address is required'}, status=400)
    
    staff_profile.crypto_wallet_address = wallet_address
    staff_profile.save()
    
    return JsonResponse({'success': True, 'wallet_address': wallet_address})
