from django.apps import AppConfig
import os


class HeartgridConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'heartgrid'
class CoreConfig(AppConfig):
    default_auto_field = "django.db.models.BigAutoField"
    name = "core"

    def ready(self):
        from django.contrib.auth import get_user_model
        from django.db.utils import OperationalError, ProgrammingError

        User = get_user_model()
        username = os.environ.get("DJANGO_SUPERUSER_USERNAME")
        email = os.environ.get("DJANGO_SUPERUSER_EMAIL")
        password = os.environ.get("DJANGO_SUPERUSER_PASSWORD")

        if username and password:
            try:
                if not User.objects.filter(username=username).exists():
                    User.objects.create_superuser(
                        username=username,
                        email=email,
                        password=password,
                    )
                    print("✅ Superuser created successfully.")
            except (OperationalError, ProgrammingError):
                # Database might not be ready during migrate
                pass
