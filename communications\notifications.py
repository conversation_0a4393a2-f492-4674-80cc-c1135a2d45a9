"""
Notification system for HeartGrid Communications

This module handles real-time notifications for messages and user status changes.
"""

import json
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync
from django.contrib.auth import get_user_model
from django.utils import timezone
from .models import UserPresence, Conversation, Message, CallSession

User = get_user_model()


class NotificationManager:
    """
    Manager class for handling real-time notifications
    """
    
    def __init__(self):
        self.channel_layer = get_channel_layer()
    
    def send_message_notification(self, message, exclude_user=None):
        """
        Send notification for a new message to all conversation participants
        
        Args:
            message: Message instance
            exclude_user: User to exclude from notifications (usually the sender)
        """
        conversation = message.conversation
        participants = conversation.participants.all()
        
        if exclude_user:
            participants = participants.exclude(id=exclude_user.id)
        
        notification_data = {
            'type': 'message_notification',
            'message_id': str(message.id),
            'conversation_id': str(conversation.id),
            'sender_id': str(message.sender.id),
            'sender_name': message.sender.name,
            'content': message.content,
            'message_type': message.message_type,
            'created_at': message.created_at.isoformat(),
            'conversation_type': conversation.conversation_type
        }
        
        for participant in participants:
            self._send_to_user(participant.id, notification_data)
    
    def send_presence_notification(self, user, status, last_seen=None, call_availability=None):
        """
        Send presence update notification to relevant users

        Args:
            user: User whose presence changed
            status: New presence status
            last_seen: Last seen timestamp (for offline status)
            call_availability: Call availability status
        """
        # Get users who should receive this notification
        relevant_users = self._get_relevant_users_for_presence(user)

        notification_data = {
            'type': 'presence_notification',
            'user_id': str(user.id),
            'user_name': user.name,
            'status': status,
            'last_seen': last_seen.isoformat() if last_seen else None,
            'call_availability': call_availability
        }

        for user_id in relevant_users:
            self._send_to_user(user_id, notification_data)

    def send_call_availability_notification(self, user, call_availability, current_call_session=None):
        """
        Send call availability update notification

        Args:
            user: User whose call availability changed
            call_availability: New call availability status
            current_call_session: Current call session if in_call
        """
        # Get users who should receive this notification
        relevant_users = self._get_relevant_users_for_presence(user)

        notification_data = {
            'type': 'call_availability_notification',
            'user_id': str(user.id),
            'user_name': user.name,
            'call_availability': call_availability,
            'current_call_session_id': str(current_call_session.id) if current_call_session else None,
            'timestamp': timezone.now().isoformat()
        }

        for user_id in relevant_users:
            self._send_to_user(user_id, notification_data)
    
    def send_typing_notification(self, conversation, user, is_typing):
        """
        Send typing indicator notification to conversation participants
        
        Args:
            conversation: Conversation instance
            user: User who is typing
            is_typing: Boolean indicating if user is typing
        """
        participants = conversation.participants.exclude(id=user.id)
        
        notification_data = {
            'type': 'typing_notification',
            'conversation_id': str(conversation.id),
            'user_id': str(user.id),
            'user_name': user.name,
            'is_typing': is_typing
        }
        
        for participant in participants:
            self._send_to_user(participant.id, notification_data)
    
    def send_message_reaction_notification(self, message, user, emoji, action):
        """
        Send message reaction notification to conversation participants
        
        Args:
            message: Message instance
            user: User who reacted
            emoji: Reaction emoji
            action: 'add' or 'remove'
        """
        conversation = message.conversation
        participants = conversation.participants.exclude(id=user.id)
        
        notification_data = {
            'type': 'reaction_notification',
            'message_id': str(message.id),
            'conversation_id': str(conversation.id),
            'user_id': str(user.id),
            'user_name': user.name,
            'emoji': emoji,
            'action': action,
            'reactions': message.reactions
        }
        
        for participant in participants:
            self._send_to_user(participant.id, notification_data)
    
    def send_conversation_notification(self, conversation, notification_type, data=None):
        """
        Send conversation-related notifications

        Args:
            conversation: Conversation instance
            notification_type: Type of notification
            data: Additional notification data
        """
        participants = conversation.participants.all()

        notification_data = {
            'type': 'conversation_notification',
            'conversation_id': str(conversation.id),
            'notification_type': notification_type,
            'data': data or {}
        }

        for participant in participants:
            self._send_to_user(participant.id, notification_data)

    def send_call_notification(self, call_session, notification_type, data=None):
        """
        Send call-related notifications

        Args:
            call_session: CallSession instance
            notification_type: Type of call notification ('incoming', 'answered', 'rejected', 'ended', 'missed')
            data: Additional notification data
        """
        notification_data = {
            'type': 'call_notification',
            'call_session_id': str(call_session.id),
            'notification_type': notification_type,
            'caller_id': str(call_session.caller.id),
            'caller_name': call_session.caller.name,
            'callee_id': str(call_session.callee.id),
            'callee_name': call_session.callee.name,
            'call_type': call_session.call_type,
            'call_state': call_session.call_state,
            'created_at': call_session.created_at.isoformat(),
            'data': data or {}
        }

        # Send to both participants
        self._send_to_user(call_session.caller.id, notification_data)
        self._send_to_user(call_session.callee.id, notification_data)

    def send_incoming_call_notification(self, call_session):
        """
        Send incoming call notification to callee

        Args:
            call_session: CallSession instance
        """
        notification_data = {
            'type': 'incoming_call',
            'call_session_id': str(call_session.id),
            'caller_id': str(call_session.caller.id),
            'caller_name': call_session.caller.name,
            'call_type': call_session.call_type,
            'conversation_id': str(call_session.conversation.id) if call_session.conversation else None,
            'created_at': call_session.created_at.isoformat()
        }

        # Send only to callee
        self._send_to_user(call_session.callee.id, notification_data)

        # Also send browser notification if supported
        self._send_browser_notification(
            call_session.callee.id,
            f"Incoming {call_session.call_type} call",
            f"{call_session.caller.name} is calling you",
            'call'
        )

    def send_call_state_notification(self, call_session, previous_state=None):
        """
        Send call state change notification

        Args:
            call_session: CallSession instance
            previous_state: Previous call state
        """
        notification_data = {
            'type': 'call_state_change',
            'call_session_id': str(call_session.id),
            'caller_id': str(call_session.caller.id),
            'callee_id': str(call_session.callee.id),
            'call_state': call_session.call_state,
            'previous_state': previous_state,
            'call_type': call_session.call_type,
            'updated_at': call_session.updated_at.isoformat()
        }

        # Send to both participants
        self._send_to_user(call_session.caller.id, notification_data)
        self._send_to_user(call_session.callee.id, notification_data)
    
    def _send_to_user(self, user_id, notification_data):
        """
        Send notification to a specific user

        Args:
            user_id: User ID to send notification to
            notification_data: Notification data dictionary
        """
        if not self.channel_layer:
            return

        group_name = f'user_{user_id}'

        async_to_sync(self.channel_layer.group_send)(
            group_name,
            {
                'type': 'send_notification',
                'notification': notification_data
            }
        )

    def _send_browser_notification(self, user_id, title, body, notification_type='general'):
        """
        Send browser notification to a specific user

        Args:
            user_id: User ID to send notification to
            title: Notification title
            body: Notification body
            notification_type: Type of notification for styling/behavior
        """
        notification_data = {
            'type': 'browser_notification',
            'title': title,
            'body': body,
            'notification_type': notification_type,
            'timestamp': timezone.now().isoformat()
        }

        self._send_to_user(user_id, notification_data)
    
    def _get_relevant_users_for_presence(self, user):
        """
        Get list of users who should receive presence updates for a user
        
        Args:
            user: User whose presence changed
            
        Returns:
            list: List of user IDs who should receive the update
        """
        relevant_users = set()
        
        # Get users from active conversations
        conversations = Conversation.objects.filter(
            participants=user,
            is_active=True
        ).prefetch_related('participants')
        
        for conversation in conversations:
            for participant in conversation.participants.exclude(id=user.id):
                relevant_users.add(str(participant.id))
        
        return list(relevant_users)


# Global notification manager instance
notification_manager = NotificationManager()


def notify_new_message(message, exclude_user=None):
    """
    Convenience function to send new message notification
    
    Args:
        message: Message instance
        exclude_user: User to exclude from notifications
    """
    notification_manager.send_message_notification(message, exclude_user)


def notify_presence_change(user, status, last_seen=None, call_availability=None):
    """
    Convenience function to send presence change notification

    Args:
        user: User whose presence changed
        status: New presence status
        last_seen: Last seen timestamp
        call_availability: Call availability status
    """
    notification_manager.send_presence_notification(user, status, last_seen, call_availability)


def notify_call_availability_change(user, call_availability, current_call_session=None):
    """
    Convenience function to send call availability change notification

    Args:
        user: User whose call availability changed
        call_availability: New call availability status
        current_call_session: Current call session if in_call
    """
    notification_manager.send_call_availability_notification(user, call_availability, current_call_session)


def notify_typing_status(conversation, user, is_typing):
    """
    Convenience function to send typing status notification
    
    Args:
        conversation: Conversation instance
        user: User who is typing
        is_typing: Boolean indicating if user is typing
    """
    notification_manager.send_typing_notification(conversation, user, is_typing)


def notify_message_reaction(message, user, emoji, action):
    """
    Convenience function to send message reaction notification

    Args:
        message: Message instance
        user: User who reacted
        emoji: Reaction emoji
        action: 'add' or 'remove'
    """
    notification_manager.send_message_reaction_notification(message, user, emoji, action)


def notify_incoming_call(call_session):
    """
    Convenience function to send incoming call notification

    Args:
        call_session: CallSession instance
    """
    notification_manager.send_incoming_call_notification(call_session)


def notify_call_state_change(call_session, previous_state=None):
    """
    Convenience function to send call state change notification

    Args:
        call_session: CallSession instance
        previous_state: Previous call state
    """
    notification_manager.send_call_state_notification(call_session, previous_state)


def notify_call_event(call_session, notification_type, data=None):
    """
    Convenience function to send general call event notification

    Args:
        call_session: CallSession instance
        notification_type: Type of call notification
        data: Additional notification data
    """
    notification_manager.send_call_notification(call_session, notification_type, data)


def notify_conversation_update(conversation, notification_type, data=None):
    """
    Convenience function to send conversation update notification
    
    Args:
        conversation: Conversation instance
        notification_type: Type of notification
        data: Additional notification data
    """
    notification_manager.send_conversation_notification(conversation, notification_type, data)
