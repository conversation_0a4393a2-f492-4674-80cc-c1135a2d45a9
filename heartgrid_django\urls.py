"""
URL configuration for HeartGrid Django project.

HeartGrid - Modern Dating Application with Premium Features
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static

urlpatterns = [
    # Admin
    path('admin/', admin.site.urls),

    # Authentication (Django Allauth)
    path('accounts/', include('allauth.urls')),

    # OAuth2 Provider
    path('o/', include('oauth2_provider.urls', namespace='oauth2_provider')),

    # HeartGrid API (both v1 and direct access)
    path('api/v1/', include('heartgrid.urls')),
    path('api/', include('heartgrid.urls')),  # Direct API access for tests

    # Communications API
    path('api/v1/communications/', include('communications.urls', namespace='communications-v1')),
    path('api/communications/', include('communications.urls', namespace='communications')),

    # Staff Management
    path('staff/', include('staff.urls', namespace='staff')),

    # HeartGrid Frontend Views
    path('', include('heartgrid.frontend_urls')),

    # API Documentation (if needed)
    # path('api/docs/', include_docs_urls(title='HeartGrid API')),
]

# Serve media files in development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
