"""
HeartGrid Django Serializers

This module contains all the DRF serializers for the HeartGrid dating platform.
"""

from rest_framework import serializers
from django.contrib.auth import authenticate
from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError
from datetime import date, datetime, timedelta
from .models import (
    User, Profile, Photo, Like, Match, Message, 
    Subscription, CryptoPayment, Notification, 
    UserActivity, Achievement
)


class UserRegistrationSerializer(serializers.ModelSerializer):
    """
    Serializer for user registration
    """
    password = serializers.CharField(write_only=True, validators=[validate_password])
    password_confirm = serializers.CharField(write_only=True)
    
    class Meta:
        model = User
        fields = ['email', 'name', 'date_of_birth', 'password', 'password_confirm']
        
    def validate(self, attrs):
        if attrs['password'] != attrs['password_confirm']:
            raise serializers.ValidationError("Passwords don't match.")
        
        # Validate age (must be 18+)
        if attrs.get('date_of_birth'):
            today = date.today()
            age = today.year - attrs['date_of_birth'].year - (
                (today.month, today.day) < (attrs['date_of_birth'].month, attrs['date_of_birth'].day)
            )
            if age < 18:
                raise serializers.ValidationError("You must be at least 18 years old to join HeartGrid.")
            if age > 100:
                raise serializers.ValidationError("Please enter a valid date of birth.")
        
        return attrs
    
    def create(self, validated_data):
        validated_data.pop('password_confirm')
        password = validated_data.pop('password')

        # Create user using custom manager
        user = User.objects.create_user(
            password=password,
            **validated_data
        )
        
        # Create profile and subscription
        Profile.objects.create(user=user)
        
        # Create 3-day trial subscription
        from django.utils import timezone
        trial_end = timezone.now() + timedelta(days=3)
        Subscription.objects.create(
            user=user,
            plan='trial',
            status='active',
            expires_at=trial_end,
            features=['basic_chat']
        )
        
        return user


class UserLoginSerializer(serializers.Serializer):
    """
    Serializer for user login
    """
    email = serializers.EmailField()
    password = serializers.CharField(write_only=True)
    
    def validate(self, attrs):
        email = attrs.get('email')
        password = attrs.get('password')
        
        if email and password:
            user = authenticate(username=email, password=password)
            if not user:
                raise serializers.ValidationError('Invalid email or password.')
            if not user.is_active:
                raise serializers.ValidationError('User account is disabled.')
            attrs['user'] = user
        else:
            raise serializers.ValidationError('Must include email and password.')
        
        return attrs


class UserSerializer(serializers.ModelSerializer):
    """
    Serializer for user data
    """
    class Meta:
        model = User
        fields = ['id', 'email', 'name', 'date_of_birth', 'age', 'is_active', 'created_at', 'auth_method']
        read_only_fields = ['id', 'age', 'created_at', 'auth_method']


class PhotoSerializer(serializers.ModelSerializer):
    """
    Serializer for user photos
    """
    class Meta:
        model = Photo
        fields = ['id', 'image', 'is_primary', 'order', 'uploaded_at']
        read_only_fields = ['id', 'uploaded_at']


class ProfileSerializer(serializers.ModelSerializer):
    """
    Serializer for user profiles
    """
    photos = PhotoSerializer(many=True, read_only=True)
    user = UserSerializer(read_only=True)
    compatibility_score = serializers.SerializerMethodField()
    shared_interests = serializers.SerializerMethodField()

    class Meta:
        model = Profile
        fields = [
            'user', 'bio', 'gender', 'interested_in', 'location',
            'country', 'city', 'preferred_genders', 'min_age_preference',
            'max_age_preference', 'location_radius', 'interests',
            'is_complete', 'is_visible', 'photos', 'compatibility_score',
            'shared_interests', 'created_at', 'updated_at'
        ]
        read_only_fields = ['is_complete', 'compatibility_score', 'shared_interests', 'created_at', 'updated_at']

    def get_compatibility_score(self, obj):
        """Get compatibility score with the requesting user"""
        request = self.context.get('request')
        if request and request.user.is_authenticated and hasattr(request.user, 'profile'):
            return request.user.profile.get_compatibility_score(obj)
        return None

    def get_shared_interests(self, obj):
        """Get shared interests with the requesting user"""
        request = self.context.get('request')
        if request and request.user.is_authenticated and hasattr(request.user, 'profile'):
            return request.user.profile.get_shared_interests(obj)
        return []

    def validate_preferred_genders(self, value):
        """Validate preferred genders list"""
        if not isinstance(value, list):
            raise serializers.ValidationError("Preferred genders must be a list.")

        valid_genders = ['male', 'female', 'non_binary', 'other', 'everyone']
        for gender in value:
            if gender not in valid_genders:
                raise serializers.ValidationError(f"Invalid gender preference: {gender}")

        if len(value) == 0:
            raise serializers.ValidationError("At least one gender preference must be selected.")

        return value

    def validate_interests(self, value):
        """Validate interests list"""
        if not isinstance(value, list):
            raise serializers.ValidationError("Interests must be a list.")

        # Get valid interests from the model
        valid_interests = Profile.INTEREST_CHOICES
        for interest in value:
            if interest not in valid_interests:
                raise serializers.ValidationError(f"Invalid interest: {interest}")

        return value

    def validate(self, attrs):
        """Cross-field validation"""
        min_age = attrs.get('min_age_preference')
        max_age = attrs.get('max_age_preference')

        if min_age and max_age:
            if min_age >= max_age:
                raise serializers.ValidationError("Maximum age preference must be greater than minimum age preference.")
            if min_age < 18:
                raise serializers.ValidationError("Minimum age preference must be at least 18.")
            if max_age > 100:
                raise serializers.ValidationError("Maximum age preference cannot exceed 100.")

        return attrs


class LikeSerializer(serializers.ModelSerializer):
    """
    Serializer for likes
    """
    liker = UserSerializer(read_only=True)
    liked = UserSerializer(read_only=True)
    
    class Meta:
        model = Like
        fields = ['id', 'liker', 'liked', 'is_super_like', 'created_at']
        read_only_fields = ['id', 'created_at']


class MatchSerializer(serializers.ModelSerializer):
    """
    Serializer for matches
    """
    user1 = UserSerializer(read_only=True)
    user2 = UserSerializer(read_only=True)
    
    class Meta:
        model = Match
        fields = ['id', 'user1', 'user2', 'created_at', 'is_active']
        read_only_fields = ['id', 'created_at']


class MessageSerializer(serializers.ModelSerializer):
    """
    Serializer for messages
    """
    sender = UserSerializer(read_only=True)
    receiver = UserSerializer(read_only=True)
    
    class Meta:
        model = Message
        fields = [
            'id', 'sender', 'receiver', 'message_type', 'content', 
            'media_file', 'is_read', 'reactions', 'created_at', 'read_at'
        ]
        read_only_fields = ['id', 'sender', 'receiver', 'created_at', 'read_at']


class SubscriptionSerializer(serializers.ModelSerializer):
    """
    Serializer for subscriptions
    """
    user = UserSerializer(read_only=True)
    
    class Meta:
        model = Subscription
        fields = [
            'id', 'user', 'plan', 'status', 'features', 
            'created_at', 'updated_at', 'expires_at'
        ]
        read_only_fields = ['id', 'user', 'created_at', 'updated_at']


class NotificationSerializer(serializers.ModelSerializer):
    """
    Serializer for notifications
    """
    related_user = UserSerializer(read_only=True)
    
    class Meta:
        model = Notification
        fields = [
            'id', 'notification_type', 'title', 'message', 
            'related_user', 'is_read', 'created_at', 'read_at'
        ]
        read_only_fields = ['id', 'created_at', 'read_at']


class AchievementSerializer(serializers.ModelSerializer):
    """
    Serializer for achievements
    """
    class Meta:
        model = Achievement
        fields = ['id', 'achievement_type', 'title', 'description', 'icon', 'earned_at']
        read_only_fields = ['id', 'earned_at']


class UserActivitySerializer(serializers.ModelSerializer):
    """
    Serializer for user activities
    """
    class Meta:
        model = UserActivity
        fields = ['id', 'activity_type', 'metadata', 'created_at']
        read_only_fields = ['id', 'created_at']


class CryptoPaymentSerializer(serializers.ModelSerializer):
    """
    Serializer for crypto payments
    """
    user = UserSerializer(read_only=True)
    
    class Meta:
        model = CryptoPayment
        fields = [
            'id', 'user', 'subscription_plan', 'chain', 'token_type',
            'payment_address', 'amount_crypto', 'amount_usd', 'tx_hash',
            'status', 'created_at', 'confirmed_at', 'expires_at'
        ]
        read_only_fields = ['id', 'user', 'created_at', 'confirmed_at']
