# Generated by Django 5.2.3 on 2025-09-26 23:39

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='StaffProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('staff_type', models.CharField(choices=[('support', 'Support Staff'), ('moderator', 'Moderator'), ('admin', 'Administrator')], max_length=50)),
                ('referral_code', models.CharField(max_length=20, unique=True)),
                ('crypto_wallet_address', models.CharField(blank=True, max_length=100)),
                ('total_earnings', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('commission_rate', models.DecimalField(decimal_places=2, default=10, max_digits=4)),
                ('date_joined', models.DateTimeField(auto_now_add=True)),
                ('is_active', models.<PERSON>oleanField(default=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='Earnings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('date', models.DateTimeField(auto_now_add=True)),
                ('source', models.CharField(choices=[('referral', 'Referral Commission'), ('bonus', 'Performance Bonus'), ('salary', 'Base Salary')], max_length=50)),
                ('description', models.TextField(blank=True)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('processed', 'Processed'), ('paid', 'Paid')], default='pending', max_length=20)),
                ('transaction_id', models.CharField(blank=True, max_length=100)),
                ('staff', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='staff.staffprofile')),
            ],
            options={
                'ordering': ['-date'],
            },
        ),
        migrations.CreateModel(
            name='Referral',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date_referred', models.DateTimeField(auto_now_add=True)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('active', 'Active'), ('converted', 'Converted')], max_length=20)),
                ('conversion_date', models.DateTimeField(blank=True, null=True)),
                ('commission_earned', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('referred_user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('staff', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='staff.staffprofile')),
            ],
            options={
                'unique_together': {('staff', 'referred_user')},
            },
        ),
    ]
