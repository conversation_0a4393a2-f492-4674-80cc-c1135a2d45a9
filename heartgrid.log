Watching for file changes with StatReloader
Watching for file changes with StatReloader
Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\utils\deprecation.py", line 119, in __call__
    response = self.process_request(request)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\http\request.py", line 191, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\utils\deprecation.py", line 119, in __call__
    response = self.process_request(request)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\http\request.py", line 191, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid_django\settings.py changed, reloading.
Watching for file changes with StatReloader
Internal Server Error: /api/v1/auth/register/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\views\generic\base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\rest_framework\generics.py", line 194, in post
    return self.create(request, *args, **kwargs)
           ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\views.py", line 45, in create
    user = serializer.save()
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\rest_framework\serializers.py", line 210, in save
    self.instance = self.create(validated_data)
                    ~~~~~~~~~~~^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\serializers.py", line 50, in create
    user = User.objects.create_user(**validated_data)
TypeError: UserManager.create_user() missing 1 required positional argument: 'username'
Bad Request: /api/v1/auth/login/
C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\models.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\serializers.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\models.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\models.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\serializers.py changed, reloading.
Watching for file changes with StatReloader
Internal Server Error: /api/v1/auth/register/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\views\generic\base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\rest_framework\generics.py", line 194, in post
    return self.create(request, *args, **kwargs)
           ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\views.py", line 45, in create
    user = serializer.save()
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\rest_framework\serializers.py", line 210, in save
    self.instance = self.create(validated_data)
                    ~~~~~~~~~~~^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\serializers.py", line 62, in create
    Subscription.objects.create(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        user=user,
        ^^^^^^^^^^
    ...<3 lines>...
        features=['basic_chat']
        ^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\db\models\query.py", line 663, in create
    obj.save(force_insert=True, using=self.db)
    ~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\models.py", line 303, in save
    if self.expires_at and self.expires_at <= timezone.now() and self.status == 'active':
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: can't compare offset-naive and offset-aware datetimes
Internal Server Error: /api/v1/auth/login/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\views\generic\base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\rest_framework\decorators.py", line 50, in handler
    return func(*args, **kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\views.py", line 75, in user_login
    token, created = Token.objects.get_or_create(user=user)
                     ^^^^^^^^^^^^^
AttributeError: type object 'Token' has no attribute 'objects'
C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\serializers.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid_django\settings.py changed, reloading.
Watching for file changes with StatReloader
Bad Request: /api/v1/auth/register/
C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\views.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\urls.py changed, reloading.
Watching for file changes with StatReloader
Unauthorized: /api/v1/profiles/
Unauthorized: /api/v1/discover/
Unauthorized: /api/v1/subscription/
Unauthorized: /api/v1/notifications/
Unauthorized: /api/v1/stats/
C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid_django\settings.py changed, reloading.
Watching for file changes with StatReloader
Unauthorized: /api/v1/profiles/
Unauthorized: /api/v1/discover/
Unauthorized: /api/v1/subscription/
Unauthorized: /api/v1/notifications/
Unauthorized: /api/v1/stats/
Watching for file changes with StatReloader
C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\views.py changed, reloading.
C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\views.py changed, reloading.
C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\urls.py changed, reloading.
C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\urls.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\admin.py changed, reloading.
C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\admin.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\views.py changed, reloading.
C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\urls.py changed, reloading.
C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\urls.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Unauthorized: /api/v1/profiles/
Unauthorized: /api/v1/discover/
Unauthorized: /api/v1/subscription/
Unauthorized: /api/v1/notifications/
Unauthorized: /api/v1/stats/
Not Found: /api/v1/profile/
Not Found: /
Unauthorized: /api/v1/
Watching for file changes with StatReloader
Unauthorized: /api/v1/
"GET /api/v1/ HTTP/1.1" 401 58
"POST /api/v1/auth/register/ HTTP/1.1" 201 344
Not Found: /api/v1/profile/
"GET /api/v1/profile/ HTTP/1.1" 404 12754
"GET /api/v1/discover/ HTTP/1.1" 200 25
"GET /api/v1/matches/ HTTP/1.1" 200 14
"GET /api/v1/subscription/ HTTP/1.1" 200 495
Not Found: /api/v1/crypto/chains/
"GET /api/v1/crypto/chains/ HTTP/1.1" 404 12772
Not Found: /api/v1/gamification/stats/
"GET /api/v1/gamification/stats/ HTTP/1.1" 404 12787
Not Found: /api/v1/gamification/achievements/
"GET /api/v1/gamification/achievements/ HTTP/1.1" 404 12808
Not Found: /api/v1/gamification/leaderboard/
"GET /api/v1/gamification/leaderboard/ HTTP/1.1" 404 12805
Unauthorized: /api/v1/
"GET /api/v1/ HTTP/1.1" 401 58
Bad Request: /api/v1/auth/register/
"POST /api/v1/auth/register/ HTTP/1.1" 400 50
Unauthorized: /api/v1/
"GET /api/v1/ HTTP/1.1" 401 58
"POST /api/v1/auth/register/ HTTP/1.1" 201 354
"GET /api/v1/profiles/ HTTP/1.1" 200 496
"GET /api/v1/discover/ HTTP/1.1" 200 25
"GET /api/v1/matches/ HTTP/1.1" 200 14
"GET /api/v1/subscription/ HTTP/1.1" 200 505
"GET /api/v1/payment/chains/ HTTP/1.1" 200 803
"GET /api/v1/stats/ HTTP/1.1" 200 872
"GET /api/v1/achievements/ HTTP/1.1" 200 1597
"GET /api/v1/leaderboard/ HTTP/1.1" 200 350
Not Found: /
"GET / HTTP/1.1" 404 2953
Not Found: /favicon.ico
"GET /favicon.ico HTTP/1.1" 404 3004
C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\views.py changed, reloading.
C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Internal Server Error: /
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\views.py", line 42, in index
    return render(request, 'index.html')
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\backends\django.py", line 79, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\engine.py", line 177, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\engine.py", line 159, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 196, in compile_nodelist
    nodelist = parser.parse()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader_tags.py", line 299, in do_extends
    nodelist = parser.parse()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader_tags.py", line 234, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 489, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 487, in parse
    filter_expression = self.compile_filter(token.contents)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 609, in compile_filter
    return FilterExpression(token, self)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 710, in __init__
    raise TemplateSyntaxError(
    ...<2 lines>...
    )
django.template.exceptions.TemplateSyntaxError: Could not parse the remainder: '('register')' from 'url_for('register')'
"GET / HTTP/1.1" **********
- Broken pipe from ('127.0.0.1', 60600)
Watching for file changes with StatReloader
Internal Server Error: /
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\views.py", line 42, in index
    return render(request, 'index.html')
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader_tags.py", line 134, in render
    compiled_parent = self.get_parent(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader_tags.py", line 131, in get_parent
    return self.find_template(parent, context)
           ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader_tags.py", line 109, in find_template
    template, origin = context.template.engine.find_template(
                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        template_name,
        ^^^^^^^^^^^^^^
        skip=history,
        ^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\engine.py", line 159, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 196, in compile_nodelist
    nodelist = parser.parse()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 489, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 487, in parse
    filter_expression = self.compile_filter(token.contents)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 609, in compile_filter
    return FilterExpression(token, self)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 710, in __init__
    raise TemplateSyntaxError(
    ...<2 lines>...
    )
django.template.exceptions.TemplateSyntaxError: Could not parse the remainder: '('static', filename='css/style.css')' from 'url_for('static', filename='css/style.css')'
"GET / HTTP/1.1" **********
Watching for file changes with StatReloader
"GET / HTTP/1.1" 200 13984
"GET /static/css/style.css HTTP/1.1" 200 12612
"GET /static/js/app.js HTTP/1.1" 200 12477
"GET / HTTP/1.1" 200 13984
"GET / HTTP/1.1" 200 13984
"GET /static/css/style.css HTTP/1.1" 200 12612
"GET /static/js/app.js HTTP/1.1" 200 12477
Not Found: /favicon.ico
"GET /favicon.ico HTTP/1.1" 404 5928
Internal Server Error: /login/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\views.py", line 48, in login_page
    return render(request, 'login.html')
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\backends\django.py", line 79, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\engine.py", line 177, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\engine.py", line 159, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 196, in compile_nodelist
    nodelist = parser.parse()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader_tags.py", line 299, in do_extends
    nodelist = parser.parse()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader_tags.py", line 234, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 489, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 487, in parse
    filter_expression = self.compile_filter(token.contents)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 609, in compile_filter
    return FilterExpression(token, self)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 710, in __init__
    raise TemplateSyntaxError(
    ...<2 lines>...
    )
django.template.exceptions.TemplateSyntaxError: Could not parse the remainder: '('google_auth.google_login')' from 'url_for('google_auth.google_login')'
"GET /login/ HTTP/1.1" **********
"GET / HTTP/1.1" 200 13984
Internal Server Error: /login/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\views.py", line 48, in login_page
    return render(request, 'login.html')
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\backends\django.py", line 79, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\engine.py", line 177, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\engine.py", line 159, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 196, in compile_nodelist
    nodelist = parser.parse()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader_tags.py", line 299, in do_extends
    nodelist = parser.parse()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader_tags.py", line 234, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 489, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 487, in parse
    filter_expression = self.compile_filter(token.contents)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 609, in compile_filter
    return FilterExpression(token, self)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 710, in __init__
    raise TemplateSyntaxError(
    ...<2 lines>...
    )
django.template.exceptions.TemplateSyntaxError: Could not parse the remainder: '('google_auth.google_login')' from 'url_for('google_auth.google_login')'
"GET /login/ HTTP/1.1" **********
Internal Server Error: /register/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\views.py", line 54, in register_page
    return render(request, 'register.html')
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\backends\django.py", line 79, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\engine.py", line 177, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\engine.py", line 159, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 196, in compile_nodelist
    nodelist = parser.parse()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader_tags.py", line 299, in do_extends
    nodelist = parser.parse()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader_tags.py", line 234, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 489, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 487, in parse
    filter_expression = self.compile_filter(token.contents)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 609, in compile_filter
    return FilterExpression(token, self)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 710, in __init__
    raise TemplateSyntaxError(
    ...<2 lines>...
    )
django.template.exceptions.TemplateSyntaxError: Could not parse the remainder: '('login')' from 'url_for('login')'
"GET /register/ HTTP/1.1" **********
"GET /discover/ HTTP/1.1" 302 0
"GET /accounts/login/?next=/discover/ HTTP/1.1" 200 2361
"GET /profile/ HTTP/1.1" 302 0
"GET /accounts/login/?next=/profile/ HTTP/1.1" 200 2359
"GET /matches/ HTTP/1.1" 302 0
"GET /accounts/login/?next=/matches/ HTTP/1.1" 200 2359
"GET /chat/ HTTP/1.1" 302 0
"GET /accounts/login/?next=/chat/ HTTP/1.1" 200 2353
"GET /subscription/ HTTP/1.1" 302 0
"GET /accounts/login/?next=/subscription/ HTTP/1.1" 200 2369
"GET /notifications/ HTTP/1.1" 302 0
"GET /accounts/login/?next=/notifications/ HTTP/1.1" 200 2371
"GET /gamification/ HTTP/1.1" 302 0
"GET /accounts/login/?next=/gamification/ HTTP/1.1" 200 2369
"GET / HTTP/1.1" 200 13984
"GET /login/ HTTP/1.1" 200 10545
"GET /register/ HTTP/1.1" 200 11915
"GET /discover/ HTTP/1.1" 302 0
"GET /accounts/login/?next=/discover/ HTTP/1.1" 200 2361
"GET /profile/ HTTP/1.1" 302 0
"GET /accounts/login/?next=/profile/ HTTP/1.1" 200 2359
"GET /matches/ HTTP/1.1" 302 0
"GET /accounts/login/?next=/matches/ HTTP/1.1" 200 2359
"GET /chat/ HTTP/1.1" 302 0
"GET /accounts/login/?next=/chat/ HTTP/1.1" 200 2353
"GET /subscription/ HTTP/1.1" 302 0
"GET /accounts/login/?next=/subscription/ HTTP/1.1" 200 2369
"GET /notifications/ HTTP/1.1" 302 0
"GET /accounts/login/?next=/notifications/ HTTP/1.1" 200 2371
"GET /gamification/ HTTP/1.1" 302 0
"GET /accounts/login/?next=/gamification/ HTTP/1.1" 200 2369
"GET /login/ HTTP/1.1" 200 10545
"GET /register/ HTTP/1.1" 200 11915
"GET / HTTP/1.1" 200 13984
"GET /login/ HTTP/1.1" 200 10545
"GET /register/ HTTP/1.1" 200 11915
"GET /discover/ HTTP/1.1" 302 0
"GET /accounts/login/?next=/discover/ HTTP/1.1" 200 2361
"GET /profile/ HTTP/1.1" 302 0
"GET /accounts/login/?next=/profile/ HTTP/1.1" 200 2359
"GET /matches/ HTTP/1.1" 302 0
"GET /accounts/login/?next=/matches/ HTTP/1.1" 200 2359
"GET /chat/ HTTP/1.1" 302 0
"GET /accounts/login/?next=/chat/ HTTP/1.1" 200 2353
"GET /subscription/ HTTP/1.1" 302 0
"GET /accounts/login/?next=/subscription/ HTTP/1.1" 200 2369
"GET /notifications/ HTTP/1.1" 302 0
"GET /accounts/login/?next=/notifications/ HTTP/1.1" 200 2371
"GET /gamification/ HTTP/1.1" 302 0
"GET /accounts/login/?next=/gamification/ HTTP/1.1" 200 2369
"GET /static/css/style.css HTTP/1.1" 200 12612
"GET /static/js/app.js HTTP/1.1" 200 12477
C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET / HTTP/1.1" 200 14088
"GET /login/ HTTP/1.1" 200 10791
"GET /register/ HTTP/1.1" 200 12161
"GET /discover/ HTTP/1.1" 302 0
"GET /static/css/style.css HTTP/1.1" 200 12612
"GET /static/js/app.js HTTP/1.1" 200 14943
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET /subscription/ HTTP/1.1" 302 0
Watching for file changes with StatReloader
"GET /accounts/login/?next=/subscription/ HTTP/1.1" 200 2369
Watching for file changes with StatReloader
"GET / HTTP/1.1" 200 14088
"GET /static/js/app.js HTTP/1.1" 200 14943
"GET /static/css/style.css HTTP/1.1" 200 12612
"GET /login/ HTTP/1.1" 200 10791
Not Found: /favicon.ico
"GET /favicon.ico HTTP/1.1" 404 6188
Not Found: /favicon.ico
"GET /favicon.ico HTTP/1.1" 404 6188
"GET / HTTP/1.1" 200 14088
"GET /static/css/style.css HTTP/1.1" 200 12612
"GET /static/js/app.js HTTP/1.1" 200 14943
Not Found: /favicon.ico
"GET /favicon.ico HTTP/1.1" 404 6188
"GET /register/ HTTP/1.1" 200 12161
"GET /login/ HTTP/1.1" 200 10791
"POST /login/ HTTP/1.1" 200 11128
"GET /register/ HTTP/1.1" 200 12161
Internal Server Error: /register/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\utils\decorators.py", line 192, in _view_wrapper
    result = _process_exception(request, e)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\utils\decorators.py", line 190, in _view_wrapper
    response = view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\views.py", line 101, in register_page
    if User.objects.filter(email=email).exists():
       ^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\db\models\manager.py", line 196, in __get__
    raise AttributeError(
    ...<5 lines>...
    )
AttributeError: Manager isn't available; 'auth.User' has been swapped for 'heartgrid.User'
"POST /register/ HTTP/1.1" 500 87789
C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET /register/ HTTP/1.1" 200 12161
"GET /static/css/style.css HTTP/1.1" 304 0
"GET /static/js/app.js HTTP/1.1" 200 14943
Not Found: /favicon.ico
"GET /favicon.ico HTTP/1.1" 404 6188
Not Found: /api/register/
Not Found: /api/profiles/
Not Found: /api/matches/
Not Found: /api/messages/
Not Found: /dashboard/
Not Found: /messages/
Not Found: /dashboard/
Not Found: /messages/
Not Found: /api/profiles/
Not Found: /api/matches/
Not Found: /api/messages/
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
[mHTTP GET / 200 [4.11, 127.0.0.1:60569][0m
[mHTTP GET /static/css/style.css 200 [0.15, 127.0.0.1:60577][0m
[mHTTP GET /static/js/app.js 200 [0.82, 127.0.0.1:60577][0m
Not Found: /favicon.ico
[33mHTTP GET /favicon.ico 404 [0.97, 127.0.0.1:60577][0m
[mHTTP GET /register/ 200 [0.27, 127.0.0.1:60597][0m
[mHTTP POST /register/ 200 [4.80, 127.0.0.1:60762][0m
[mHTTP POST /register/ 200 [0.11, 127.0.0.1:60821][0m
[mHTTP GET /login/ 200 [0.12, 127.0.0.1:60831][0m
[32mHTTP POST /login/ 302 [4.94, 127.0.0.1:60851][0m
Internal Server Error: /discover/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\sync.py", line 518, in thread_handler
    raise exc_info[1]
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\core\handlers\exception.py", line 42, in inner
    response = await get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\sync.py", line 518, in thread_handler
    raise exc_info[1]
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\core\handlers\base.py", line 253, in _get_response_async
    response = await wrapped_callback(
               ^^^^^^^^^^^^^^^^^^^^^^^
        request, *callback_args, **callback_kwargs
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\sync.py", line 468, in __call__
    ret = await asyncio.shield(exec_coro)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\current_thread_executor.py", line 40, in run
    result = self.fn(*self.args, **self.kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\sync.py", line 522, in thread_handler
    return func(*args, **kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\views.py", line 177, in discover_page
    return render(request, 'discover_modern.html')
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'discover' not found. 'discover' is not a valid view function or pattern name.
[35;1mHTTP GET /discover/ 500 [2.24, 127.0.0.1:60851][0m
Internal Server Error: /discover/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\sync.py", line 518, in thread_handler
    raise exc_info[1]
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\core\handlers\exception.py", line 42, in inner
    response = await get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\sync.py", line 518, in thread_handler
    raise exc_info[1]
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\core\handlers\base.py", line 253, in _get_response_async
    response = await wrapped_callback(
               ^^^^^^^^^^^^^^^^^^^^^^^
        request, *callback_args, **callback_kwargs
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\sync.py", line 468, in __call__
    ret = await asyncio.shield(exec_coro)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\current_thread_executor.py", line 40, in run
    result = self.fn(*self.args, **self.kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\sync.py", line 522, in thread_handler
    return func(*args, **kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\views.py", line 177, in discover_page
    return render(request, 'discover_modern.html')
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'discover_page' not found. 'discover_page' is not a valid view function or pattern name.
[35;1mHTTP GET /discover/ 500 [0.45, 127.0.0.1:61203][0m
Watching for file changes with StatReloader
[32mHTTP GET /login/ 302 [0.95, 127.0.0.1:61280][0m
Internal Server Error: /discover/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\sync.py", line 518, in thread_handler
    raise exc_info[1]
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\core\handlers\exception.py", line 42, in inner
    response = await get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\sync.py", line 518, in thread_handler
    raise exc_info[1]
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\core\handlers\base.py", line 253, in _get_response_async
    response = await wrapped_callback(
               ^^^^^^^^^^^^^^^^^^^^^^^
        request, *callback_args, **callback_kwargs
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\sync.py", line 468, in __call__
    ret = await asyncio.shield(exec_coro)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\current_thread_executor.py", line 40, in run
    result = self.fn(*self.args, **self.kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\sync.py", line 522, in thread_handler
    return func(*args, **kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\views.py", line 177, in discover_page
    return render(request, 'discover_modern.html')
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'discover_page' not found. 'discover_page' is not a valid view function or pattern name.
[35;1mHTTP GET /discover/ 500 [0.35, 127.0.0.1:61280][0m
[mHTTP GET / 200 [0.04, 127.0.0.1:61615][0m
[32mHTTP GET /discover/ 302 [0.01, 127.0.0.1:62026][0m
[mHTTP GET /accounts/login/?next=/discover/ 200 [9.05, 127.0.0.1:62028][0m
[32mHTTP GET /profile/ 302 [0.01, 127.0.0.1:62112][0m
[mHTTP GET /accounts/login/?next=/profile/ 200 [0.03, 127.0.0.1:62114][0m
[32mHTTP GET /messages/ 302 [0.39, 127.0.0.1:62147][0m
[mHTTP GET /accounts/login/?next=/messages/ 200 [0.32, 127.0.0.1:62149][0m
[mHTTP GET /discover/ 200 [0.21, 127.0.0.1:62202][0m
[33mHTTP GET /static/images/default-avatar.png 404 [0.07, 127.0.0.1:62202][0m
[mHTTP GET /discover/ 200 [0.04, 127.0.0.1:62659][0m
[33mHTTP GET /static/images/default-avatar.png 404 [0.08, 127.0.0.1:62659][0m
[mHTTP GET /api/v1/notifications/ 200 [0.16, 127.0.0.1:62659][0m
[mHTTP GET /api/v1/discover/ 200 [0.24, 127.0.0.1:62668][0m
[mHTTP GET /api/v1/notifications/ 200 [0.02, 127.0.0.1:62713][0m
[mHTTP GET /api/v1/discover/ 200 [0.02, 127.0.0.1:62713][0m
[mHTTP GET /api/v1/notifications/ 200 [0.02, 127.0.0.1:62736][0m
[mHTTP GET /api/v1/discover/ 200 [0.03, 127.0.0.1:62736][0m
Internal Server Error: /profile/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\sync.py", line 518, in thread_handler
    raise exc_info[1]
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\core\handlers\exception.py", line 42, in inner
    response = await get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\sync.py", line 518, in thread_handler
    raise exc_info[1]
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\core\handlers\base.py", line 253, in _get_response_async
    response = await wrapped_callback(
               ^^^^^^^^^^^^^^^^^^^^^^^
        request, *callback_args, **callback_kwargs
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\sync.py", line 468, in __call__
    ret = await asyncio.shield(exec_coro)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\current_thread_executor.py", line 40, in run
    result = self.fn(*self.args, **self.kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\sync.py", line 522, in thread_handler
    return func(*args, **kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\views.py", line 182, in profile_page
    return render(request, 'profile.html')
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\backends\django.py", line 79, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\engine.py", line 177, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\engine.py", line 159, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 196, in compile_nodelist
    nodelist = parser.parse()
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loader_tags.py", line 299, in do_extends
    nodelist = parser.parse()
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loader_tags.py", line 234, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 489, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 487, in parse
    filter_expression = self.compile_filter(token.contents)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 609, in compile_filter
    return FilterExpression(token, self)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 710, in __init__
    raise TemplateSyntaxError(
    ...<2 lines>...
    )
django.template.exceptions.TemplateSyntaxError: Could not parse the remainder: ' or ''' from 'profile.age or '''
[35;1mHTTP GET /profile/ 500 [0.95, 127.0.0.1:62742][0m
Watching for file changes with StatReloader
Internal Server Error: /profile/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\sync.py", line 518, in thread_handler
    raise exc_info[1]
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\core\handlers\exception.py", line 42, in inner
    response = await get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\sync.py", line 518, in thread_handler
    raise exc_info[1]
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\core\handlers\base.py", line 253, in _get_response_async
    response = await wrapped_callback(
               ^^^^^^^^^^^^^^^^^^^^^^^
        request, *callback_args, **callback_kwargs
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\sync.py", line 468, in __call__
    ret = await asyncio.shield(exec_coro)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\current_thread_executor.py", line 40, in run
    result = self.fn(*self.args, **self.kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\sync.py", line 522, in thread_handler
    return func(*args, **kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\views.py", line 182, in profile_page
    return render(request, 'profile.html')
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\backends\django.py", line 79, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\engine.py", line 177, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\engine.py", line 159, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 196, in compile_nodelist
    nodelist = parser.parse()
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loader_tags.py", line 299, in do_extends
    nodelist = parser.parse()
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loader_tags.py", line 234, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\defaulttags.py", line 1531, in do_with
    extra_context = token_kwargs(remaining_bits, parser, support_legacy=True)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 1127, in token_kwargs
    kwargs[key] = parser.compile_filter(value)
                  ~~~~~~~~~~~~~~~~~~~~~^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 609, in compile_filter
    return FilterExpression(token, self)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 705, in __init__
    filter_func = parser.find_filter(filter_name)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 615, in find_filter
    raise TemplateSyntaxError("Invalid filter: '%s'" % filter_name)
django.template.exceptions.TemplateSyntaxError: Invalid filter: 'split'
[35;1mHTTP GET /profile/ 500 [1.06, 127.0.0.1:51295][0m
[mHTTP GET /profile/ 200 [0.48, 127.0.0.1:51508][0m
Not Found: /subscription_status
[33mHTTP GET /subscription_status 404 [0.06, 127.0.0.1:51516][0m
Internal Server Error: /subscription/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\sync.py", line 518, in thread_handler
    raise exc_info[1]
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\core\handlers\exception.py", line 42, in inner
    response = await get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\sync.py", line 518, in thread_handler
    raise exc_info[1]
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\core\handlers\base.py", line 253, in _get_response_async
    response = await wrapped_callback(
               ^^^^^^^^^^^^^^^^^^^^^^^
        request, *callback_args, **callback_kwargs
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\sync.py", line 468, in __call__
    ret = await asyncio.shield(exec_coro)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\current_thread_executor.py", line 40, in run
    result = self.fn(*self.args, **self.kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\sync.py", line 522, in thread_handler
    return func(*args, **kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\views.py", line 198, in subscription_page
    return render(request, 'subscription.html')
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\backends\django.py", line 79, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\engine.py", line 177, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\engine.py", line 159, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 196, in compile_nodelist
    nodelist = parser.parse()
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loader_tags.py", line 299, in do_extends
    nodelist = parser.parse()
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loader_tags.py", line 234, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\defaulttags.py", line 962, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 489, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 487, in parse
    filter_expression = self.compile_filter(token.contents)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 609, in compile_filter
    return FilterExpression(token, self)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 710, in __init__
    raise TemplateSyntaxError(
    ...<2 lines>...
    )
django.template.exceptions.TemplateSyntaxError: Could not parse the remainder: '()' from 'subscription.plan.title()'
[35;1mHTTP GET /subscription/ 500 [0.44, 127.0.0.1:51904][0m
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\views.py changed, reloading.
Watching for file changes with StatReloader
[mHTTP GET /?ide_webview_request_time=1755256749609 200 [1.63, 127.0.0.1:56253][0m
[mHTTP GET /static/css/style.css 200 [1.67, 127.0.0.1:56273][0m
[mHTTP GET /static/js/app.js 200 [1.65, 127.0.0.1:56272][0m
Not Found: /@vite/client
[33mHTTP GET /@vite/client 404 [8.61, 127.0.0.1:56290][0m
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Invalid HTTP_HOST header: '127.0.0.1:8000'. You may need to add '127.0.0.1' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\utils\deprecation.py", line 119, in __call__
    response = self.process_request(request)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\http\request.py", line 191, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: '127.0.0.1:8000'. You may need to add '127.0.0.1' to ALLOWED_HOSTS.
[31;1mHTTP GET / 400 [0.95, 127.0.0.1:61058][0m
Invalid HTTP_HOST header: '127.0.0.1:8000'. You may need to add '127.0.0.1' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\utils\deprecation.py", line 119, in __call__
    response = self.process_request(request)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\http\request.py", line 191, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: '127.0.0.1:8000'. You may need to add '127.0.0.1' to ALLOWED_HOSTS.
[31;1mHTTP GET /favicon.ico 400 [0.03, 127.0.0.1:61058][0m
C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid_django\settings.py changed, reloading.
Watching for file changes with StatReloader
Invalid HTTP_HOST header: '127.0.0.1:8000'. You may need to add '127.0.0.1' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\utils\deprecation.py", line 119, in __call__
    response = self.process_request(request)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\http\request.py", line 191, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: '127.0.0.1:8000'. You may need to add '127.0.0.1' to ALLOWED_HOSTS.
[31;1mHTTP GET / 400 [0.02, 127.0.0.1:61562][0m
Invalid HTTP_HOST header: '127.0.0.1:8000'. You may need to add '127.0.0.1' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\utils\deprecation.py", line 119, in __call__
    response = self.process_request(request)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\http\request.py", line 191, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: '127.0.0.1:8000'. You may need to add '127.0.0.1' to ALLOWED_HOSTS.
[31;1mHTTP GET /favicon.ico 400 [0.07, 127.0.0.1:61562][0m
Invalid HTTP_HOST header: '127.0.0.1:8000'. You may need to add '127.0.0.1' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\utils\deprecation.py", line 119, in __call__
    response = self.process_request(request)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\http\request.py", line 191, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: '127.0.0.1:8000'. You may need to add '127.0.0.1' to ALLOWED_HOSTS.
[31;1mHTTP GET / 400 [0.04, 127.0.0.1:61562][0m
Invalid HTTP_HOST header: '127.0.0.1:8000'. You may need to add '127.0.0.1' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\utils\deprecation.py", line 119, in __call__
    response = self.process_request(request)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\http\request.py", line 191, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: '127.0.0.1:8000'. You may need to add '127.0.0.1' to ALLOWED_HOSTS.
[31;1mHTTP GET /favicon.ico 400 [0.02, 127.0.0.1:61562][0m
C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid_django\settings.py changed, reloading.
Watching for file changes with StatReloader
Invalid HTTP_HOST header: '127.0.0.1:8000'. You may need to add '127.0.0.1' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\utils\deprecation.py", line 119, in __call__
    response = self.process_request(request)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\http\request.py", line 191, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: '127.0.0.1:8000'. You may need to add '127.0.0.1' to ALLOWED_HOSTS.
[31;1mHTTP GET / 400 [0.50, 127.0.0.1:50021][0m
Invalid HTTP_HOST header: '127.0.0.1:8000'. You may need to add '127.0.0.1' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\utils\deprecation.py", line 119, in __call__
    response = self.process_request(request)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\http\request.py", line 191, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: '127.0.0.1:8000'. You may need to add '127.0.0.1' to ALLOWED_HOSTS.
[31;1mHTTP GET /favicon.ico 400 [0.01, 127.0.0.1:50021][0m
C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid_django\settings.py changed, reloading.
Watching for file changes with StatReloader
Internal Server Error: /favicon.ico
[35;1mHTTP GET /favicon.ico 500 [0.52, 127.0.0.1:58994][0m
[mHTTP GET / 200 [1.85, 127.0.0.1:58994][0m
[mHTTP GET /static/css/style.css 200 [0.39, 127.0.0.1:58994][0m
[mHTTP GET /static/js/app.js 200 [0.39, 127.0.0.1:59017][0m
Internal Server Error: /favicon.ico
[35;1mHTTP GET /favicon.ico 500 [0.25, 127.0.0.1:59017][0m
[mHTTP GET /login/ 200 [0.10, 127.0.0.1:59017][0m
Internal Server Error: /favicon.ico
[35;1mHTTP GET /favicon.ico 500 [0.02, 127.0.0.1:59053][0m
[mHTTP POST /login/ 200 [6.90, 127.0.0.1:59108][0m
Internal Server Error: /favicon.ico
[35;1mHTTP GET /favicon.ico 500 [0.09, 127.0.0.1:59108][0m
[mHTTP GET /register/ 200 [0.08, 127.0.0.1:59150][0m
Internal Server Error: /favicon.ico
[35;1mHTTP GET /favicon.ico 500 [0.23, 127.0.0.1:59150][0m
Internal Server Error: /register/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\contrib\auth\__init__.py", line 57, in _get_backend_from_user
    backend = backend or user.backend
                         ^^^^^^^^^^^^
AttributeError: 'User' object has no attribute 'backend'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\sync.py", line 518, in thread_handler
    raise exc_info[1]
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\core\handlers\exception.py", line 42, in inner
    response = await get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\sync.py", line 518, in thread_handler
    raise exc_info[1]
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\core\handlers\base.py", line 253, in _get_response_async
    response = await wrapped_callback(
               ^^^^^^^^^^^^^^^^^^^^^^^
        request, *callback_args, **callback_kwargs
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\sync.py", line 468, in __call__
    ret = await asyncio.shield(exec_coro)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\current_thread_executor.py", line 40, in run
    result = self.fn(*self.args, **self.kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\sync.py", line 522, in thread_handler
    return func(*args, **kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\utils\decorators.py", line 192, in _view_wrapper
    result = _process_exception(request, e)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\utils\decorators.py", line 190, in _view_wrapper
    response = view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\views.py", line 194, in register_page
    auth_login(request, user)
    ~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\contrib\auth\__init__.py", line 189, in login
    backend = _get_backend_from_user(user=user, backend=backend)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\contrib\auth\__init__.py", line 63, in _get_backend_from_user
    raise ValueError(
    ...<3 lines>...
    )
ValueError: You have multiple authentication backends configured and therefore must provide the `backend` argument or set the `backend` attribute on the user.
[35;1mHTTP POST /register/ 500 [12.39, 127.0.0.1:60808][0m
Internal Server Error: /favicon.ico
[35;1mHTTP GET /favicon.ico 500 [0.14, 127.0.0.1:60808][0m
C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\views.py changed, reloading.
Watching for file changes with StatReloader
[mHTTP POST /register/ 200 [1.32, 127.0.0.1:63808][0m
[36mHTTP GET /static/css/style.css 304 [0.14, 127.0.0.1:63808][0m
[36mHTTP GET /static/js/app.js 304 [0.18, 127.0.0.1:63808][0m
Internal Server Error: /favicon.ico
[35;1mHTTP GET /favicon.ico 500 [0.42, 127.0.0.1:63814][0m
[mHTTP GET /login/ 200 [0.01, 127.0.0.1:63814][0m
Internal Server Error: /favicon.ico
[35;1mHTTP GET /favicon.ico 500 [0.08, 127.0.0.1:63835][0m
[32mHTTP POST /login/ 302 [6.83, 127.0.0.1:63861][0m
[mHTTP GET /discover/ 200 [0.59, 127.0.0.1:63861][0m
Internal Server Error: /static/images/default-avatar.png
[35;1mHTTP GET /static/images/default-avatar.png 500 [0.06, 127.0.0.1:63861][0m
[mHTTP GET /api/v1/notifications/ 200 [0.06, 127.0.0.1:63880][0m
[mHTTP GET /api/v1/discover/ 200 [0.09, 127.0.0.1:63879][0m
Internal Server Error: /favicon.ico
[35;1mHTTP GET /favicon.ico 500 [0.14, 127.0.0.1:63879][0m
[mHTTP GET /api/v1/notifications/ 200 [0.02, 127.0.0.1:63972][0m
[mHTTP GET /api/v1/notifications/ 200 [0.02, 127.0.0.1:64033][0m
[mHTTP GET /api/v1/notifications/ 200 [0.02, 127.0.0.1:64091][0m
[mHTTP GET /api/v1/notifications/ 200 [0.02, 127.0.0.1:64158][0m
[mHTTP GET /api/v1/notifications/ 200 [0.03, 127.0.0.1:64210][0m
[mHTTP GET /api/v1/notifications/ 200 [0.04, 127.0.0.1:64270][0m
[mHTTP GET /api/v1/notifications/ 200 [0.10, 127.0.0.1:64322][0m
[mHTTP GET /api/v1/notifications/ 200 [0.05, 127.0.0.1:64408][0m
[mHTTP GET /api/v1/notifications/ 200 [0.04, 127.0.0.1:64494][0m
[mHTTP GET /api/v1/notifications/ 200 [0.02, 127.0.0.1:64576][0m
[mHTTP GET /api/v1/notifications/ 200 [0.01, 127.0.0.1:64704][0m
[mHTTP GET /api/v1/notifications/ 200 [0.03, 127.0.0.1:64750][0m
Internal Server Error: /subscription/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\sync.py", line 518, in thread_handler
    raise exc_info[1]
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\core\handlers\exception.py", line 42, in inner
    response = await get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\sync.py", line 518, in thread_handler
    raise exc_info[1]
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\core\handlers\base.py", line 253, in _get_response_async
    response = await wrapped_callback(
               ^^^^^^^^^^^^^^^^^^^^^^^
        request, *callback_args, **callback_kwargs
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\sync.py", line 468, in __call__
    ret = await asyncio.shield(exec_coro)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\current_thread_executor.py", line 40, in run
    result = self.fn(*self.args, **self.kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\sync.py", line 522, in thread_handler
    return func(*args, **kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\views.py", line 230, in subscription_page
    return render(request, 'subscription.html')
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\backends\django.py", line 79, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\engine.py", line 177, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\engine.py", line 159, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 196, in compile_nodelist
    nodelist = parser.parse()
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loader_tags.py", line 299, in do_extends
    nodelist = parser.parse()
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loader_tags.py", line 234, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\defaulttags.py", line 962, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 489, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 487, in parse
    filter_expression = self.compile_filter(token.contents)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 609, in compile_filter
    return FilterExpression(token, self)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 710, in __init__
    raise TemplateSyntaxError(
    ...<2 lines>...
    )
django.template.exceptions.TemplateSyntaxError: Could not parse the remainder: '()' from 'subscription.plan.title()'
[35;1mHTTP GET /subscription/ 500 [2.68, 127.0.0.1:64777][0m
Internal Server Error: /favicon.ico
[35;1mHTTP GET /favicon.ico 500 [0.82, 127.0.0.1:64777][0m
[mHTTP GET /api/v1/notifications/ 200 [0.03, 127.0.0.1:65506][0m
Internal Server Error: /favicon.ico
[35;1mHTTP GET /favicon.ico 500 [0.04, 127.0.0.1:65507][0m
Internal Server Error: /gamification/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 510, in parse
    compile_func = self.tags[command]
                   ~~~~~~~~~^^^^^^^^^
KeyError: 'set'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\sync.py", line 518, in thread_handler
    raise exc_info[1]
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\core\handlers\exception.py", line 42, in inner
    response = await get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\sync.py", line 518, in thread_handler
    raise exc_info[1]
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\core\handlers\base.py", line 253, in _get_response_async
    response = await wrapped_callback(
               ^^^^^^^^^^^^^^^^^^^^^^^
        request, *callback_args, **callback_kwargs
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\sync.py", line 468, in __call__
    ret = await asyncio.shield(exec_coro)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\current_thread_executor.py", line 40, in run
    result = self.fn(*self.args, **self.kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\sync.py", line 522, in thread_handler
    return func(*args, **kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\views.py", line 240, in gamification_page
    return render(request, 'gamification.html')
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\backends\django.py", line 79, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\engine.py", line 177, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\engine.py", line 159, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 196, in compile_nodelist
    nodelist = parser.parse()
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loader_tags.py", line 299, in do_extends
    nodelist = parser.parse()
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loader_tags.py", line 234, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 512, in parse
    self.invalid_block_tag(token, command, parse_until)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 567, in invalid_block_tag
    raise self.error(
    ...<8 lines>...
    )
django.template.exceptions.TemplateSyntaxError: Invalid block tag on line 20: 'set', expected 'endblock'. Did you forget to register or load this tag?
[35;1mHTTP GET /gamification/ 500 [0.15, 127.0.0.1:49153][0m
Internal Server Error: /favicon.ico
[35;1mHTTP GET /favicon.ico 500 [0.02, 127.0.0.1:49153][0m
Internal Server Error: /favicon.ico
[35;1mHTTP GET /favicon.ico 500 [0.02, 127.0.0.1:49374][0m
[mHTTP GET /api/v1/notifications/ 200 [0.04, 127.0.0.1:49373][0m
[mHTTP GET /api/v1/notifications/ 200 [0.02, 127.0.0.1:49416][0m
[mHTTP GET /api/v1/notifications/ 200 [0.02, 127.0.0.1:49493][0m
Internal Server Error: /gamification/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 510, in parse
    compile_func = self.tags[command]
                   ~~~~~~~~~^^^^^^^^^
KeyError: 'set'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\sync.py", line 518, in thread_handler
    raise exc_info[1]
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\core\handlers\exception.py", line 42, in inner
    response = await get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\sync.py", line 518, in thread_handler
    raise exc_info[1]
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\core\handlers\base.py", line 253, in _get_response_async
    response = await wrapped_callback(
               ^^^^^^^^^^^^^^^^^^^^^^^
        request, *callback_args, **callback_kwargs
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\sync.py", line 468, in __call__
    ret = await asyncio.shield(exec_coro)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\current_thread_executor.py", line 40, in run
    result = self.fn(*self.args, **self.kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\sync.py", line 522, in thread_handler
    return func(*args, **kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\views.py", line 240, in gamification_page
    return render(request, 'gamification.html')
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\backends\django.py", line 79, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\engine.py", line 177, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\engine.py", line 159, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 196, in compile_nodelist
    nodelist = parser.parse()
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loader_tags.py", line 299, in do_extends
    nodelist = parser.parse()
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loader_tags.py", line 234, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 512, in parse
    self.invalid_block_tag(token, command, parse_until)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 567, in invalid_block_tag
    raise self.error(
    ...<8 lines>...
    )
django.template.exceptions.TemplateSyntaxError: Invalid block tag on line 20: 'set', expected 'endblock'. Did you forget to register or load this tag?
[35;1mHTTP GET /gamification/ 500 [0.06, 127.0.0.1:49493][0m
Internal Server Error: /favicon.ico
[35;1mHTTP GET /favicon.ico 500 [0.04, 127.0.0.1:49493][0m
Internal Server Error: /favicon.ico
[35;1mHTTP GET /favicon.ico 500 [0.02, 127.0.0.1:49798][0m
[mHTTP GET /api/v1/notifications/ 200 [0.08, 127.0.0.1:49797][0m
[mHTTP GET /profile/ 200 [0.68, 127.0.0.1:49797][0m
[36mHTTP GET /static/css/style.css 304 [0.05, 127.0.0.1:49797][0m
[36mHTTP GET /static/js/app.js 304 [0.01, 127.0.0.1:49812][0m
Internal Server Error: /favicon.ico
[35;1mHTTP GET /favicon.ico 500 [0.02, 127.0.0.1:49812][0m
Internal Server Error: /notifications/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\sync.py", line 518, in thread_handler
    raise exc_info[1]
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\core\handlers\exception.py", line 42, in inner
    response = await get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\sync.py", line 518, in thread_handler
    raise exc_info[1]
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\core\handlers\base.py", line 253, in _get_response_async
    response = await wrapped_callback(
               ^^^^^^^^^^^^^^^^^^^^^^^
        request, *callback_args, **callback_kwargs
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\sync.py", line 468, in __call__
    ret = await asyncio.shield(exec_coro)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\current_thread_executor.py", line 40, in run
    result = self.fn(*self.args, **self.kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\sync.py", line 522, in thread_handler
    return func(*args, **kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\views.py", line 235, in notifications_page
    return render(request, 'notifications.html')
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\backends\django.py", line 79, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\engine.py", line 177, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\engine.py", line 159, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 196, in compile_nodelist
    nodelist = parser.parse()
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loader_tags.py", line 299, in do_extends
    nodelist = parser.parse()
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loader_tags.py", line 234, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\defaulttags.py", line 862, in do_for
    nodelist_loop = parser.parse(
        (
    ...<2 lines>...
        )
    )
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 489, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 487, in parse
    filter_expression = self.compile_filter(token.contents)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 609, in compile_filter
    return FilterExpression(token, self)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 710, in __init__
    raise TemplateSyntaxError(
    ...<2 lines>...
    )
django.template.exceptions.TemplateSyntaxError: Could not parse the remainder: ' if not notification.read' from ''unread' if not notification.read'
[35;1mHTTP GET /notifications/ 500 [0.20, 127.0.0.1:49905][0m
Internal Server Error: /favicon.ico
[35;1mHTTP GET /favicon.ico 500 [0.02, 127.0.0.1:49905][0m
[mHTTP GET /profile/ 200 [0.02, 127.0.0.1:50184][0m
Internal Server Error: /favicon.ico
[35;1mHTTP GET /favicon.ico 500 [0.17, 127.0.0.1:50184][0m
Internal Server Error: /favicon.ico
[35;1mHTTP GET /favicon.ico 500 [0.01, 127.0.0.1:50200][0m
Internal Server Error: /subscription/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\sync.py", line 518, in thread_handler
    raise exc_info[1]
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\core\handlers\exception.py", line 42, in inner
    response = await get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\sync.py", line 518, in thread_handler
    raise exc_info[1]
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\core\handlers\base.py", line 253, in _get_response_async
    response = await wrapped_callback(
               ^^^^^^^^^^^^^^^^^^^^^^^
        request, *callback_args, **callback_kwargs
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\sync.py", line 468, in __call__
    ret = await asyncio.shield(exec_coro)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\current_thread_executor.py", line 40, in run
    result = self.fn(*self.args, **self.kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\sync.py", line 522, in thread_handler
    return func(*args, **kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\views.py", line 230, in subscription_page
    return render(request, 'subscription.html')
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\backends\django.py", line 79, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\engine.py", line 177, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\engine.py", line 159, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 196, in compile_nodelist
    nodelist = parser.parse()
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loader_tags.py", line 299, in do_extends
    nodelist = parser.parse()
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loader_tags.py", line 234, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\defaulttags.py", line 962, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 489, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 487, in parse
    filter_expression = self.compile_filter(token.contents)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 609, in compile_filter
    return FilterExpression(token, self)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 710, in __init__
    raise TemplateSyntaxError(
    ...<2 lines>...
    )
django.template.exceptions.TemplateSyntaxError: Could not parse the remainder: '()' from 'subscription.plan.title()'
[35;1mHTTP GET /subscription/ 500 [0.31, 127.0.0.1:50262][0m
Internal Server Error: /favicon.ico
[35;1mHTTP GET /favicon.ico 500 [0.02, 127.0.0.1:50262][0m
Internal Server Error: /favicon.ico
[35;1mHTTP GET /favicon.ico 500 [0.13, 127.0.0.1:50262][0m
[mHTTP GET /profile/ 200 [0.03, 127.0.0.1:50310][0m
[36mHTTP GET /static/css/style.css 304 [0.02, 127.0.0.1:50310][0m
[36mHTTP GET /static/js/app.js 304 [0.01, 127.0.0.1:50311][0m
Internal Server Error: /favicon.ico
[35;1mHTTP GET /favicon.ico 500 [0.16, 127.0.0.1:50310][0m
[mHTTP GET /discover/ 200 [0.05, 127.0.0.1:50310][0m
Internal Server Error: /static/images/default-avatar.png
[35;1mHTTP GET /static/images/default-avatar.png 500 [0.03, 127.0.0.1:50310][0m
[mHTTP GET /api/v1/discover/ 200 [0.11, 127.0.0.1:50311][0m
Internal Server Error: /favicon.ico
[mHTTP GET /api/v1/notifications/ 200 [0.14, 127.0.0.1:50310][0m
[35;1mHTTP GET /favicon.ico 500 [0.13, 127.0.0.1:50333][0m
[mHTTP GET /matches/ 200 [0.49, 127.0.0.1:50341][0m
[mHTTP GET /api/v1/matches/?min_compatibility=0&sort_by=compatibility 200 [0.12, 127.0.0.1:50341][0m
Internal Server Error: /favicon.ico
[35;1mHTTP GET /favicon.ico 500 [0.03, 127.0.0.1:50343][0m
[mHTTP GET /discover/ 200 [0.03, 127.0.0.1:50343][0m
Internal Server Error: /static/images/default-avatar.png
[35;1mHTTP GET /static/images/default-avatar.png 500 [0.17, 127.0.0.1:50343][0m
[mHTTP GET /api/v1/notifications/ 200 [0.10, 127.0.0.1:50343][0m
[mHTTP GET /api/v1/discover/ 200 [0.23, 127.0.0.1:50354][0m
Internal Server Error: /favicon.ico
[35;1mHTTP GET /favicon.ico 500 [0.07, 127.0.0.1:50357][0m
Internal Server Error: /messages/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\sync.py", line 518, in thread_handler
    raise exc_info[1]
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\core\handlers\exception.py", line 42, in inner
    response = await get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\sync.py", line 518, in thread_handler
    raise exc_info[1]
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\core\handlers\base.py", line 253, in _get_response_async
    response = await wrapped_callback(
               ^^^^^^^^^^^^^^^^^^^^^^^
        request, *callback_args, **callback_kwargs
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\sync.py", line 468, in __call__
    ret = await asyncio.shield(exec_coro)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\current_thread_executor.py", line 40, in run
    result = self.fn(*self.args, **self.kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\sync.py", line 522, in thread_handler
    return func(*args, **kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\views.py", line 225, in chat_page
    return render(request, 'chat.html', context)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\backends\django.py", line 79, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\engine.py", line 177, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\engine.py", line 159, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 196, in compile_nodelist
    nodelist = parser.parse()
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loader_tags.py", line 299, in do_extends
    nodelist = parser.parse()
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loader_tags.py", line 234, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 489, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 487, in parse
    filter_expression = self.compile_filter(token.contents)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 609, in compile_filter
    return FilterExpression(token, self)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 710, in __init__
    raise TemplateSyntaxError(
    ...<2 lines>...
    )
django.template.exceptions.TemplateSyntaxError: Could not parse the remainder: ' if match_user else 'User'' from 'match_user.name if match_user else 'User''
[35;1mHTTP GET /messages/ 500 [0.07, 127.0.0.1:50357][0m
Internal Server Error: /favicon.ico
[35;1mHTTP GET /favicon.ico 500 [0.02, 127.0.0.1:50357][0m
Watching for file changes with StatReloader
[mHTTP GET / 200 [2.11, 127.0.0.1:50793][0m
[36mHTTP GET /static/js/app.js 304 [0.05, 127.0.0.1:50794][0m
[36mHTTP GET /static/css/style.css 304 [0.07, 127.0.0.1:50793][0m
Internal Server Error: /favicon.ico
[35;1mHTTP GET /favicon.ico 500 [0.10, 127.0.0.1:50793][0m
Internal Server Error: /subscription/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\sync.py", line 518, in thread_handler
    raise exc_info[1]
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\core\handlers\exception.py", line 42, in inner
    response = await get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\sync.py", line 518, in thread_handler
    raise exc_info[1]
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\core\handlers\base.py", line 253, in _get_response_async
    response = await wrapped_callback(
               ^^^^^^^^^^^^^^^^^^^^^^^
        request, *callback_args, **callback_kwargs
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\sync.py", line 468, in __call__
    ret = await asyncio.shield(exec_coro)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\current_thread_executor.py", line 40, in run
    result = self.fn(*self.args, **self.kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\sync.py", line 522, in thread_handler
    return func(*args, **kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\views.py", line 230, in subscription_page
    return render(request, 'subscription.html')
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\backends\django.py", line 79, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\engine.py", line 177, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\engine.py", line 159, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 196, in compile_nodelist
    nodelist = parser.parse()
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loader_tags.py", line 299, in do_extends
    nodelist = parser.parse()
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loader_tags.py", line 234, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\defaulttags.py", line 962, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 489, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 487, in parse
    filter_expression = self.compile_filter(token.contents)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 609, in compile_filter
    return FilterExpression(token, self)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 710, in __init__
    raise TemplateSyntaxError(
    ...<2 lines>...
    )
django.template.exceptions.TemplateSyntaxError: Could not parse the remainder: '('%B %d, %Y at %I:%M %p')' from 'subscription.expires_at.strftime('%B %d, %Y at %I:%M %p')'
[35;1mHTTP GET /subscription/ 500 [11.51, 127.0.0.1:50813][0m
Internal Server Error: /favicon.ico
[35;1mHTTP GET /favicon.ico 500 [0.03, 127.0.0.1:50813][0m
[mHTTP GET / 200 [0.02, 127.0.0.1:50914][0m
Internal Server Error: /favicon.ico
[35;1mHTTP GET /favicon.ico 500 [0.21, 127.0.0.1:50916][0m
Internal Server Error: /gamification/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 510, in parse
    compile_func = self.tags[command]
                   ~~~~~~~~~^^^^^^^^^
KeyError: 'set'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\sync.py", line 518, in thread_handler
    raise exc_info[1]
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\core\handlers\exception.py", line 42, in inner
    response = await get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\sync.py", line 518, in thread_handler
    raise exc_info[1]
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\core\handlers\base.py", line 253, in _get_response_async
    response = await wrapped_callback(
               ^^^^^^^^^^^^^^^^^^^^^^^
        request, *callback_args, **callback_kwargs
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\sync.py", line 468, in __call__
    ret = await asyncio.shield(exec_coro)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\current_thread_executor.py", line 40, in run
    result = self.fn(*self.args, **self.kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\sync.py", line 522, in thread_handler
    return func(*args, **kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\views.py", line 240, in gamification_page
    return render(request, 'gamification.html')
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\backends\django.py", line 79, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\engine.py", line 177, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\engine.py", line 159, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 196, in compile_nodelist
    nodelist = parser.parse()
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loader_tags.py", line 299, in do_extends
    nodelist = parser.parse()
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loader_tags.py", line 234, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 512, in parse
    self.invalid_block_tag(token, command, parse_until)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 567, in invalid_block_tag
    raise self.error(
    ...<8 lines>...
    )
django.template.exceptions.TemplateSyntaxError: Invalid block tag on line 20: 'set', expected 'endblock'. Did you forget to register or load this tag?
[35;1mHTTP GET /gamification/ 500 [0.66, 127.0.0.1:50916][0m
Internal Server Error: /favicon.ico
[35;1mHTTP GET /favicon.ico 500 [0.01, 127.0.0.1:50916][0m
Internal Server Error: /favicon.ico
[35;1mHTTP GET /favicon.ico 500 [0.14, 127.0.0.1:50989][0m
Internal Server Error: /notifications/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\sync.py", line 518, in thread_handler
    raise exc_info[1]
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\core\handlers\exception.py", line 42, in inner
    response = await get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\sync.py", line 518, in thread_handler
    raise exc_info[1]
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\core\handlers\base.py", line 253, in _get_response_async
    response = await wrapped_callback(
               ^^^^^^^^^^^^^^^^^^^^^^^
        request, *callback_args, **callback_kwargs
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\sync.py", line 468, in __call__
    ret = await asyncio.shield(exec_coro)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\current_thread_executor.py", line 40, in run
    result = self.fn(*self.args, **self.kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\sync.py", line 522, in thread_handler
    return func(*args, **kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\views.py", line 235, in notifications_page
    return render(request, 'notifications.html')
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\backends\django.py", line 79, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\engine.py", line 177, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\engine.py", line 159, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 196, in compile_nodelist
    nodelist = parser.parse()
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loader_tags.py", line 299, in do_extends
    nodelist = parser.parse()
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loader_tags.py", line 234, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\defaulttags.py", line 862, in do_for
    nodelist_loop = parser.parse(
        (
    ...<2 lines>...
        )
    )
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 489, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 487, in parse
    filter_expression = self.compile_filter(token.contents)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 609, in compile_filter
    return FilterExpression(token, self)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 710, in __init__
    raise TemplateSyntaxError(
    ...<2 lines>...
    )
django.template.exceptions.TemplateSyntaxError: Could not parse the remainder: '[:16]' from 'notification.created_at[:16]'
[35;1mHTTP GET /notifications/ 500 [0.12, 127.0.0.1:50989][0m
Internal Server Error: /favicon.ico
[35;1mHTTP GET /favicon.ico 500 [0.02, 127.0.0.1:50989][0m
Internal Server Error: /favicon.ico
[35;1mHTTP GET /favicon.ico 500 [0.14, 127.0.0.1:51055][0m
Internal Server Error: /favicon.ico
[35;1mHTTP GET /favicon.ico 500 [0.01, 127.0.0.1:51062][0m
Internal Server Error: /notifications/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\sync.py", line 518, in thread_handler
    raise exc_info[1]
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\core\handlers\exception.py", line 42, in inner
    response = await get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\sync.py", line 518, in thread_handler
    raise exc_info[1]
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\core\handlers\base.py", line 253, in _get_response_async
    response = await wrapped_callback(
               ^^^^^^^^^^^^^^^^^^^^^^^
        request, *callback_args, **callback_kwargs
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\sync.py", line 468, in __call__
    ret = await asyncio.shield(exec_coro)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\current_thread_executor.py", line 40, in run
    result = self.fn(*self.args, **self.kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\asgiref\sync.py", line 522, in thread_handler
    return func(*args, **kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\views.py", line 235, in notifications_page
    return render(request, 'notifications.html')
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\backends\django.py", line 79, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\engine.py", line 177, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\engine.py", line 159, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 196, in compile_nodelist
    nodelist = parser.parse()
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loader_tags.py", line 299, in do_extends
    nodelist = parser.parse()
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\loader_tags.py", line 234, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\defaulttags.py", line 862, in do_for
    nodelist_loop = parser.parse(
        (
    ...<2 lines>...
        )
    )
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 489, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 487, in parse
    filter_expression = self.compile_filter(token.contents)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 609, in compile_filter
    return FilterExpression(token, self)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\template\base.py", line 710, in __init__
    raise TemplateSyntaxError(
    ...<2 lines>...
    )
django.template.exceptions.TemplateSyntaxError: Could not parse the remainder: '[:16]' from 'notification.created_at[:16]'
[35;1mHTTP GET /notifications/ 500 [0.48, 127.0.0.1:51304][0m
Internal Server Error: /favicon.ico
[35;1mHTTP GET /favicon.ico 500 [0.07, 127.0.0.1:51304][0m
Internal Server Error: /favicon.ico
[35;1mHTTP GET /favicon.ico 500 [0.08, 127.0.0.1:51311][0m
C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid_django\settings.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid_django\settings.py changed, reloading.
Watching for file changes with StatReloader
