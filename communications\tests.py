"""
Tests for HeartGrid Communications

This module contains tests for the communications models, views, and WebSocket functionality.
"""

from django.test import TestCase, TransactionTestCase
from django.contrib.auth import get_user_model
from django.urls import reverse
from rest_framework.test import APITestCase
from rest_framework import status
from channels.testing import Websocket<PERSON>ommunicator
from channels.db import database_sync_to_async
import json

from .models import (
    UserPresence, Conversation, Message, MessageStatus,
    ConversationParticipant
)
from .consumers import ChatConsumer, PresenceConsumer
from .utils import create_direct_conversation, send_message

User = get_user_model()


class CommunicationsModelsTest(TestCase):
    """Test communications models"""

    def setUp(self):
        self.user1 = User.objects.create_user(
            email='<EMAIL>',
            name='User One',
            password='testpass123'
        )
        self.user2 = User.objects.create_user(
            email='<EMAIL>',
            name='User Two',
            password='testpass123'
        )

    def test_user_presence_creation(self):
        """Test UserPresence model creation"""
        presence, created = UserPresence.objects.get_or_create(user=self.user1)
        self.assertEqual(presence.status, 'offline')
        self.assertIsNotNone(presence.created_at)

    def test_conversation_creation(self):
        """Test Conversation model creation"""
        conversation = Conversation.objects.create(conversation_type='direct')
        conversation.participants.add(self.user1, self.user2)

        self.assertEqual(conversation.participants.count(), 2)
        self.assertEqual(conversation.conversation_type, 'direct')
        self.assertTrue(conversation.is_active)

    def test_message_creation(self):
        """Test Message model creation"""
        conversation = create_direct_conversation(self.user1, self.user2)
        message = send_message(conversation, self.user1, "Hello, World!")

        self.assertEqual(message.content, "Hello, World!")
        self.assertEqual(message.sender, self.user1)
        self.assertEqual(message.conversation, conversation)
        self.assertFalse(message.is_deleted)
