"""
Django Admin Configuration for HeartGrid Communications

This module contains admin interface configurations for communications models.
"""

from django.contrib import admin
from .models import (
    UserPresence, Conversation, Message, MessageStatus,
    ConversationParticipant, CallSession, UserCallSettings
)


@admin.register(UserPresence)
class UserPresenceAdmin(admin.ModelAdmin):
    """Admin interface for UserPresence model"""
    list_display = ['user', 'status', 'call_availability', 'last_seen', 'last_activity', 'is_typing_to']
    list_filter = ['status', 'call_availability', 'created_at', 'updated_at']
    search_fields = ['user__name', 'user__email']
    readonly_fields = ['created_at', 'updated_at']


class ConversationParticipantInline(admin.TabularInline):
    """Inline admin for conversation participants"""
    model = ConversationParticipant
    extra = 0
    readonly_fields = ['created_at', 'updated_at']


@admin.register(Conversation)
class ConversationAdmin(admin.ModelAdmin):
    """Admin interface for Conversation model"""
    list_display = ['id', 'conversation_type', 'created_at', 'last_message_at', 'is_active']
    list_filter = ['conversation_type', 'is_active', 'is_archived', 'created_at']
    search_fields = ['participants__name', 'participants__email']
    readonly_fields = ['id', 'created_at', 'updated_at', 'last_message_at']
    inlines = [ConversationParticipantInline]

    def get_queryset(self, request):
        return super().get_queryset(request).prefetch_related('participants')


class MessageStatusInline(admin.TabularInline):
    """Inline admin for message statuses"""
    model = MessageStatus
    extra = 0
    readonly_fields = ['created_at', 'updated_at']


@admin.register(Message)
class MessageAdmin(admin.ModelAdmin):
    """Admin interface for Message model"""
    list_display = ['id', 'sender', 'conversation', 'message_type', 'created_at', 'is_deleted']
    list_filter = ['message_type', 'is_deleted', 'created_at']
    search_fields = ['sender__name', 'sender__email', 'content']
    readonly_fields = ['id', 'created_at', 'updated_at', 'edited_at', 'deleted_at']
    inlines = [MessageStatusInline]

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('sender', 'conversation')


@admin.register(MessageStatus)
class MessageStatusAdmin(admin.ModelAdmin):
    """Admin interface for MessageStatus model"""
    list_display = ['message', 'user', 'status', 'read_at', 'created_at']
    list_filter = ['status', 'created_at', 'read_at']
    search_fields = ['user__name', 'user__email', 'message__content']
    readonly_fields = ['created_at', 'updated_at']

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('message', 'user')


@admin.register(ConversationParticipant)
class ConversationParticipantAdmin(admin.ModelAdmin):
    """Admin interface for ConversationParticipant model"""
    list_display = ['conversation', 'user', 'is_muted', 'is_archived', 'notifications_enabled', 'last_read_at']
    list_filter = ['is_muted', 'is_archived', 'notifications_enabled', 'created_at']
    search_fields = ['user__name', 'user__email']
    readonly_fields = ['created_at', 'updated_at']

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('conversation', 'user')


@admin.register(CallSession)
class CallSessionAdmin(admin.ModelAdmin):
    """Admin interface for CallSession model"""
    list_display = [
        'id', 'caller', 'callee', 'call_type', 'state',
        'started_at', 'answered_at', 'ended_at', 'duration_seconds'
    ]
    list_filter = ['call_type', 'state', 'started_at', 'end_reason']
    search_fields = ['caller__name', 'caller__email', 'callee__name', 'callee__email']
    readonly_fields = [
        'id', 'created_at', 'updated_at', 'started_at',
        'duration', 'duration_seconds'
    ]

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('caller', 'callee', 'conversation')


@admin.register(UserCallSettings)
class UserCallSettingsAdmin(admin.ModelAdmin):
    """Admin interface for UserCallSettings model"""
    list_display = [
        'user', 'voice_calls_enabled', 'video_calls_enabled',
        'auto_reject_calls', 'call_notifications_enabled', 'preferred_video_quality'
    ]
    list_filter = [
        'voice_calls_enabled', 'video_calls_enabled', 'auto_reject_calls',
        'call_notifications_enabled', 'preferred_video_quality', 'allow_calls_from_strangers'
    ]
    search_fields = ['user__name', 'user__email']
    readonly_fields = ['created_at', 'updated_at']

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user')
