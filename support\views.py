"""
HeartGrid Support System Views

Comprehensive support system views for:
- Support ticket management
- FAQ system
- Safety reporting
- Live chat support
- Account verification
"""

from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.core.paginator import Paginator
from django.db.models import Q, Count
from django.utils import timezone
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status

from .models import (
    SupportTicket, SupportMessage, SupportCategory, FAQ, 
    SafetyReport, LiveChatSession, AccountVerification
)
from .serializers import (
    SupportTicketSerializer, FAQSerializer, SafetyReportSerializer
)


@login_required
def support_dashboard(request):
    """Main support dashboard"""
    user_tickets = SupportTicket.objects.filter(user=request.user)
    
    context = {
        'open_tickets': user_tickets.filter(status__in=['open', 'in_progress']).count(),
        'total_tickets': user_tickets.count(),
        'recent_tickets': user_tickets[:5],
        'categories': SupportCategory.objects.filter(is_active=True),
    }
    
    return render(request, 'support/dashboard.html', context)


@login_required
def create_ticket(request):
    """Create new support ticket"""
    if request.method == 'POST':
        category_id = request.POST.get('category')
        subject = request.POST.get('subject')
        description = request.POST.get('description')
        
        if not all([category_id, subject, description]):
            messages.error(request, 'Please fill in all required fields.')
            return redirect('support:create_ticket')
        
        try:
            category = SupportCategory.objects.get(id=category_id)
            
            ticket = SupportTicket.objects.create(
                user=request.user,
                category=category,
                subject=subject,
                description=description,
                priority=category.priority_level,
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                ip_address=request.META.get('REMOTE_ADDR')
            )
            
            # Create initial message
            SupportMessage.objects.create(
                ticket=ticket,
                sender=request.user,
                message_type='user',
                content=description
            )
            
            # Send auto-response if configured
            if category.auto_response_template:
                SupportMessage.objects.create(
                    ticket=ticket,
                    sender=request.user,  # System user in real implementation
                    message_type='auto',
                    content=category.auto_response_template
                )
            
            messages.success(request, f'Support ticket {ticket.ticket_number} created successfully!')
            return redirect('support:ticket_detail', ticket_id=ticket.id)
            
        except SupportCategory.DoesNotExist:
            messages.error(request, 'Invalid category selected.')
    
    context = {
        'categories': SupportCategory.objects.filter(is_active=True)
    }
    
    return render(request, 'support/create_ticket.html', context)


@login_required
def ticket_detail(request, ticket_id):
    """View support ticket details"""
    ticket = get_object_or_404(SupportTicket, id=ticket_id, user=request.user)
    
    if request.method == 'POST':
        content = request.POST.get('message')
        if content:
            SupportMessage.objects.create(
                ticket=ticket,
                sender=request.user,
                message_type='user',
                content=content
            )
            
            # Update ticket status if closed
            if ticket.status == 'closed':
                ticket.status = 'open'
                ticket.save()
            
            messages.success(request, 'Message sent successfully!')
            return redirect('support:ticket_detail', ticket_id=ticket.id)
    
    # Mark messages as read by user
    ticket.messages.filter(read_by_user=False).exclude(sender=request.user).update(read_by_user=True)
    
    context = {
        'ticket': ticket,
        'messages': ticket.messages.all(),
        'can_reply': ticket.status not in ['closed', 'resolved']
    }
    
    return render(request, 'support/ticket_detail.html', context)


@login_required
def my_tickets(request):
    """List user's support tickets"""
    tickets = SupportTicket.objects.filter(user=request.user)
    
    # Filter by status
    status_filter = request.GET.get('status')
    if status_filter:
        tickets = tickets.filter(status=status_filter)
    
    # Search
    search = request.GET.get('search')
    if search:
        tickets = tickets.filter(
            Q(subject__icontains=search) | 
            Q(ticket_number__icontains=search) |
            Q(description__icontains=search)
        )
    
    paginator = Paginator(tickets, 10)
    page = request.GET.get('page')
    tickets = paginator.get_page(page)
    
    context = {
        'tickets': tickets,
        'status_filter': status_filter,
        'search': search,
        'status_choices': SupportTicket.STATUS_CHOICES
    }
    
    return render(request, 'support/my_tickets.html', context)


def faq_list(request):
    """FAQ listing page"""
    category = request.GET.get('category')
    search = request.GET.get('search')
    
    faqs = FAQ.objects.filter(is_published=True)
    
    if category:
        faqs = faqs.filter(category=category)
    
    if search:
        faqs = faqs.filter(
            Q(question__icontains=search) | 
            Q(answer__icontains=search)
        )
    
    # Group FAQs by category
    faq_categories = {}
    for faq in faqs:
        if faq.category not in faq_categories:
            faq_categories[faq.category] = []
        faq_categories[faq.category].append(faq)
    
    context = {
        'faq_categories': faq_categories,
        'categories': FAQ.CATEGORIES,
        'selected_category': category,
        'search': search
    }
    
    return render(request, 'support/faq.html', context)


@require_http_methods(["POST"])
def faq_helpful(request, faq_id):
    """Mark FAQ as helpful"""
    faq = get_object_or_404(FAQ, id=faq_id)
    faq.helpful_count += 1
    faq.save()
    
    return JsonResponse({'status': 'success', 'helpful_count': faq.helpful_count})


@login_required
def safety_report(request):
    """Create safety report"""
    if request.method == 'POST':
        reported_user_id = request.POST.get('reported_user')
        report_type = request.POST.get('report_type')
        description = request.POST.get('description')
        
        if not all([reported_user_id, report_type, description]):
            messages.error(request, 'Please fill in all required fields.')
            return redirect('support:safety_report')
        
        try:
            from heartgrid.models import User
            reported_user = User.objects.get(id=reported_user_id)
            
            if reported_user == request.user:
                messages.error(request, 'You cannot report yourself.')
                return redirect('support:safety_report')
            
            # Check if already reported recently
            recent_report = SafetyReport.objects.filter(
                reporter=request.user,
                reported_user=reported_user,
                created_at__gte=timezone.now() - timezone.timedelta(days=7)
            ).exists()
            
            if recent_report:
                messages.warning(request, 'You have already reported this user recently.')
                return redirect('support:safety_report')
            
            SafetyReport.objects.create(
                reporter=request.user,
                reported_user=reported_user,
                report_type=report_type,
                description=description
            )
            
            messages.success(request, 'Safety report submitted successfully. We will review it promptly.')
            return redirect('support:dashboard')
            
        except User.DoesNotExist:
            messages.error(request, 'User not found.')
    
    context = {
        'report_types': SafetyReport.REPORT_TYPES
    }
    
    return render(request, 'support/safety_report.html', context)


@login_required
def account_verification(request):
    """Account verification request"""
    if request.method == 'POST':
        verification_type = request.POST.get('verification_type')
        
        if not verification_type:
            messages.error(request, 'Please select a verification type.')
            return redirect('support:account_verification')
        
        # Check if already has pending verification
        existing = AccountVerification.objects.filter(
            user=request.user,
            verification_type=verification_type,
            status='pending'
        ).exists()
        
        if existing:
            messages.warning(request, 'You already have a pending verification request of this type.')
            return redirect('support:account_verification')
        
        AccountVerification.objects.create(
            user=request.user,
            verification_type=verification_type,
            verification_data={
                'submitted_at': timezone.now().isoformat(),
                'user_agent': request.META.get('HTTP_USER_AGENT', ''),
            }
        )
        
        messages.success(request, 'Verification request submitted successfully!')
        return redirect('support:account_verification')
    
    # Get user's verification requests
    verifications = AccountVerification.objects.filter(user=request.user)
    
    context = {
        'verifications': verifications,
        'verification_types': AccountVerification.VERIFICATION_TYPES
    }
    
    return render(request, 'support/account_verification.html', context)


# API Views
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def api_support_tickets(request):
    """API endpoint for user's support tickets"""
    tickets = SupportTicket.objects.filter(user=request.user)
    serializer = SupportTicketSerializer(tickets, many=True)
    return Response(serializer.data)


@api_view(['GET'])
def api_faq(request):
    """API endpoint for FAQ"""
    faqs = FAQ.objects.filter(is_published=True)
    category = request.GET.get('category')
    
    if category:
        faqs = faqs.filter(category=category)
    
    serializer = FAQSerializer(faqs, many=True)
    return Response(serializer.data)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def api_safety_report(request):
    """API endpoint for safety reports"""
    serializer = SafetyReportSerializer(data=request.data)
    
    if serializer.is_valid():
        serializer.save(reporter=request.user)
        return Response({'message': 'Safety report submitted successfully'}, 
                       status=status.HTTP_201_CREATED)
    
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
