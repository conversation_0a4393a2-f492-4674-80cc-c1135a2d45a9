{% extends 'base.html' %}
{% load static %}

{% block title %}Earnings History{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Earnings History</h1>
    </div>

    <!-- Filter Form -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Filter Earnings</h6>
        </div>
        <div class="card-body">
            <form method="get" class="form-inline">
                <div class="form-group mx-sm-3 mb-2">
                    <label for="start_date" class="sr-only">Start Date</label>
                    <input type="date" class="form-control" id="start_date" name="start_date"
                           value="{{ start_date|date:'Y-m-d' }}">
                </div>
                <div class="form-group mx-sm-3 mb-2">
                    <label for="end_date" class="sr-only">End Date</label>
                    <input type="date" class="form-control" id="end_date" name="end_date"
                           value="{{ end_date|date:'Y-m-d' }}">
                </div>
                <button type="submit" class="btn btn-primary mb-2">Filter</button>
                <a href="{% url 'staff:earnings_history' %}" class="btn btn-secondary mb-2 ml-2">Reset</a>
            </form>
        </div>
    </div>

    <!-- Earnings Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Earnings Details</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="earningsTable">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Amount</th>
                            <th>Source</th>
                            <th>Description</th>
                            <th>Status</th>
                            <th>Transaction ID</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for earning in earnings %}
                        <tr>
                            <td>{{ earning.date|date:"M d, Y H:i" }}</td>
                            <td>${{ earning.amount }}</td>
                            <td>{{ earning.get_source_display }}</td>
                            <td>{{ earning.description|default:"-" }}</td>
                            <td>
                                <span class="badge badge-{{ earning.status }}">
                                    {{ earning.get_status_display }}
                                </span>
                            </td>
                            <td>{{ earning.transaction_id|default:"-" }}</td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="6" class="text-center">No earnings found for the selected period</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    $('#earningsTable').DataTable({
        "order": [[0, "desc"]],
        "pageLength": 25,
        "language": {
            "search": "Search earnings:",
            "lengthMenu": "Show _MENU_ entries per page",
            "info": "Showing _START_ to _END_ of _TOTAL_ earnings",
            "infoEmpty": "Showing 0 to 0 of 0 earnings",
            "infoFiltered": "(filtered from _MAX_ total earnings)"
        }
    });
});
</script>
{% endblock %}