from django.test import TestCase
from django.utils import timezone
from datetime import timezone as dt_timezone, datetime, timedelta
from django.contrib.auth import get_user_model
from .models import ReferralLink, Subscription

class ReferralLinkTests(TestCase):
    def setUp(self):
        self.User = get_user_model()
        self.inviter = self.User.objects.create_user(email='<EMAIL>', password='testpass', name='Inviter')

    def test_generate_referral_link(self):
        expires = timezone.now() + timedelta(days=7)
        link = ReferralLink.objects.create(inviter=self.inviter, expires_at=expires)
        self.assertTrue(link.is_valid())
        self.assertEqual(link.inviter, self.inviter)
        self.assertTrue(link.expires_at > timezone.now())

    def test_referral_link_expiry(self):
        expires = timezone.now() - timedelta(days=1)
        link = ReferralLink.objects.create(inviter=self.inviter, expires_at=expires)
        self.assertFalse(link.is_valid())

    def test_register_with_referral_link(self):
        expires = timezone.now() + timedelta(days=7)
        link = ReferralLink.objects.create(inviter=self.inviter, expires_at=expires)
        referred_user = self.User.objects.create_user(email='<EMAIL>', password='testpass', name='Referred')
        # Simulate registration logic
        if link.is_valid():
            Subscription.objects.create(user=referred_user, plan='FULL', status='ACTIVE', expires_at=datetime(2024,9,1,0,0,0, tzinfo=dt_timezone.utc))
        sub = Subscription.objects.filter(user=referred_user).first()
        self.assertIsNotNone(sub)
        self.assertEqual(sub.plan, 'FULL')
        self.assertEqual(sub.expires_at, datetime(2024,9,1,0,0,0, tzinfo=dt_timezone.utc))

    def test_register_with_expired_referral_link(self):
        expires = timezone.now() - timedelta(days=1)
        link = ReferralLink.objects.create(inviter=self.inviter, expires_at=expires)
        referred_user = self.User.objects.create_user(email='<EMAIL>', password='testpass', name='Expired')
        # Simulate registration logic
        if link.is_valid():
            Subscription.objects.create(user=referred_user, plan='FULL', status='ACTIVE', expires_at=datetime(2024,9,1,0,0,0, tzinfo=dt_timezone.utc))
        sub = Subscription.objects.filter(user=referred_user).first()
        self.assertIsNone(sub)
