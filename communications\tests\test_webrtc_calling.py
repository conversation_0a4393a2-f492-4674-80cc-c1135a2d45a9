"""
Comprehensive tests for WebRTC calling functionality in HeartGrid Communications
"""

import json
import uuid
from django.test import TestCase, TransactionTestCase
from django.contrib.auth import get_user_model
from channels.testing import WebsocketCommunicator
from channels.db import database_sync_to_async
from communications.models import (
    Conversation, CallSession, UserPresence, UserCallSettings
)
from communications.consumers import ChatConsumer
from communications.webrtc_config import WebRTCConfig
from communications.notifications import NotificationManager

User = get_user_model()


class WebRTCConfigTest(TestCase):
    """Test WebRTC configuration functionality"""
    
    def test_ice_server_configuration(self):
        """Test ICE server configuration"""
        config = WebRTCConfig.get_webrtc_configuration()
        
        # Should have ICE servers
        self.assertIn('iceServers', config)
        self.assertGreater(len(config['iceServers']), 0)
        
        # Should have STUN servers at minimum
        stun_servers = [s for s in config['iceServers'] if s['urls'].startswith('stun:')]
        self.assertGreater(len(stun_servers), 0)
    
    def test_configuration_validation(self):
        """Test configuration validation"""
        is_valid, errors = WebRTCConfig.validate_configuration()
        
        # Should be valid with default configuration
        self.assertTrue(is_valid)
        self.assertEqual(len(errors), 0)
    
    def test_media_constraints(self):
        """Test media constraints configuration"""
        constraints = WebRTCConfig.get_media_constraints()
        
        self.assertIn('audio', constraints)
        self.assertIn('video', constraints)
        
        # Audio constraints
        audio = constraints['audio']
        self.assertIn('echoCancellation', audio)
        self.assertIn('noiseSuppression', audio)
        
        # Video constraints
        video = constraints['video']
        self.assertIn('width', video)
        self.assertIn('height', video)


class CallSessionModelTest(TestCase):
    """Test CallSession model functionality"""
    
    def setUp(self):
        self.user1 = User.objects.create_user(
            username='caller',
            email='<EMAIL>',
            password='testpass123'
        )
        self.user2 = User.objects.create_user(
            username='callee',
            email='<EMAIL>',
            password='testpass123'
        )
        self.conversation = Conversation.objects.create(
            name='Test Call Conversation'
        )
        self.conversation.participants.add(self.user1, self.user2)
    
    def test_call_session_creation(self):
        """Test creating a call session"""
        call_session = CallSession.objects.create(
            caller=self.user1,
            callee=self.user2,
            conversation=self.conversation,
            call_type='voice',
            call_state='initiating'
        )
        
        self.assertEqual(call_session.caller, self.user1)
        self.assertEqual(call_session.callee, self.user2)
        self.assertEqual(call_session.call_type, 'voice')
        self.assertEqual(call_session.call_state, 'initiating')
        self.assertIsNotNone(call_session.created_at)
    
    def test_call_session_state_transitions(self):
        """Test call session state transitions"""
        call_session = CallSession.objects.create(
            caller=self.user1,
            callee=self.user2,
            conversation=self.conversation,
            call_type='video'
        )
        
        # Test state transitions
        call_session.call_state = 'ringing'
        call_session.save()
        self.assertEqual(call_session.call_state, 'ringing')
        
        call_session.call_state = 'active'
        call_session.save()
        self.assertEqual(call_session.call_state, 'active')
        
        call_session.call_state = 'ended'
        call_session.save()
        self.assertEqual(call_session.call_state, 'ended')
    
    def test_webrtc_data_storage(self):
        """Test storing WebRTC signaling data"""
        call_session = CallSession.objects.create(
            caller=self.user1,
            callee=self.user2,
            conversation=self.conversation,
            call_type='voice'
        )
        
        # Test storing WebRTC offer
        offer_data = {
            'type': 'offer',
            'sdp': 'v=0\r\no=- 123456789 2 IN IP4 127.0.0.1\r\n...'
        }
        call_session.webrtc_offer = offer_data
        call_session.save()
        
        # Reload and verify
        call_session.refresh_from_db()
        self.assertEqual(call_session.webrtc_offer['type'], 'offer')
        
        # Test storing ICE candidates
        ice_candidate = {
            'candidate': 'candidate:1 1 UDP 2130706431 ************* 54400 typ host',
            'sdpMLineIndex': 0,
            'sdpMid': 'audio'
        }
        call_session.caller_ice_candidates.append(ice_candidate)
        call_session.save()
        
        call_session.refresh_from_db()
        self.assertEqual(len(call_session.caller_ice_candidates), 1)
        self.assertEqual(call_session.caller_ice_candidates[0]['candidate'], ice_candidate['candidate'])


class UserPresenceCallTest(TestCase):
    """Test user presence integration with calling"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
    
    def test_call_availability_tracking(self):
        """Test call availability status tracking"""
        presence, created = UserPresence.objects.get_or_create(user=self.user)
        
        # Default availability
        self.assertEqual(presence.call_availability, 'available')
        
        # Set busy
        presence.call_availability = 'busy'
        presence.save()
        self.assertEqual(presence.call_availability, 'busy')
        
        # Set in call
        presence.call_availability = 'in_call'
        presence.save()
        self.assertEqual(presence.call_availability, 'in_call')
    
    def test_call_session_presence_integration(self):
        """Test integration between call sessions and presence"""
        presence, created = UserPresence.objects.get_or_create(user=self.user)
        
        # Create a call session
        call_session = CallSession.objects.create(
            caller=self.user,
            callee=self.user,  # Self-call for testing
            call_type='voice',
            call_state='active'
        )
        
        # Link call session to presence
        presence.current_call_session = call_session
        presence.call_availability = 'in_call'
        presence.save()
        
        self.assertEqual(presence.current_call_session, call_session)
        self.assertEqual(presence.call_availability, 'in_call')


class UserCallSettingsTest(TestCase):
    """Test user call settings functionality"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
    
    def test_default_call_settings(self):
        """Test default call settings"""
        settings, created = UserCallSettings.objects.get_or_create(user=self.user)
        
        # Default settings should allow calls
        self.assertTrue(settings.can_receive_voice_calls())
        self.assertTrue(settings.can_receive_video_calls())
        self.assertTrue(settings.auto_answer_enabled)
    
    def test_call_permission_checks(self):
        """Test call permission functionality"""
        settings = UserCallSettings.objects.create(
            user=self.user,
            voice_calls_enabled=False,
            video_calls_enabled=True
        )
        
        self.assertFalse(settings.can_receive_voice_calls())
        self.assertTrue(settings.can_receive_video_calls())


class NotificationSystemTest(TestCase):
    """Test call notification system"""
    
    def setUp(self):
        self.user1 = User.objects.create_user(
            username='caller',
            email='<EMAIL>',
            password='testpass123'
        )
        self.user2 = User.objects.create_user(
            username='callee',
            email='<EMAIL>',
            password='testpass123'
        )
        self.conversation = Conversation.objects.create(
            name='Test Conversation'
        )
        self.conversation.participants.add(self.user1, self.user2)
        
        self.notification_manager = NotificationManager()
    
    def test_call_notification_creation(self):
        """Test creating call notifications"""
        call_session = CallSession.objects.create(
            caller=self.user1,
            callee=self.user2,
            conversation=self.conversation,
            call_type='voice'
        )
        
        # Test incoming call notification
        notification_data = {
            'type': 'incoming_call',
            'call_session_id': str(call_session.id),
            'caller_id': str(self.user1.id),
            'caller_name': self.user1.name,
            'call_type': 'voice'
        }
        
        # Should not raise any exceptions
        self.notification_manager.send_call_notification(
            self.user2, 'incoming', notification_data
        )
    
    def test_call_availability_notification(self):
        """Test call availability change notifications"""
        # Should not raise any exceptions
        self.notification_manager.send_call_availability_notification(
            self.user1, 'busy'
        )


class WebRTCIntegrationTest(TransactionTestCase):
    """Integration tests for WebRTC calling system"""
    
    def setUp(self):
        self.user1 = User.objects.create_user(
            username='caller',
            email='<EMAIL>',
            password='testpass123'
        )
        self.user2 = User.objects.create_user(
            username='callee',
            email='<EMAIL>',
            password='testpass123'
        )
        self.conversation = Conversation.objects.create(
            name='Test Conversation'
        )
        self.conversation.participants.add(self.user1, self.user2)
    
    async def test_call_initiation_flow(self):
        """Test complete call initiation flow through WebSocket"""
        # Create WebSocket communicator
        communicator = WebsocketCommunicator(
            ChatConsumer.as_asgi(),
            f"/ws/chat/{self.conversation.id}/"
        )
        communicator.scope['user'] = self.user1
        
        connected, subprotocol = await communicator.connect()
        self.assertTrue(connected)
        
        # Send call initiation message
        await communicator.send_json_to({
            'type': 'call_initiate',
            'callee_id': str(self.user2.id),
            'call_type': 'voice'
        })
        
        # Should receive call initiated confirmation
        response = await communicator.receive_json_from()
        self.assertEqual(response['type'], 'call_initiated')
        self.assertIn('call_session_id', response)
        
        await communicator.disconnect()
    
    def test_call_session_lifecycle(self):
        """Test complete call session lifecycle"""
        # Create call session
        call_session = CallSession.objects.create(
            caller=self.user1,
            callee=self.user2,
            conversation=self.conversation,
            call_type='voice',
            call_state='initiating'
        )
        
        # Simulate state transitions
        call_session.call_state = 'ringing'
        call_session.save()
        
        call_session.call_state = 'active'
        call_session.save()
        
        call_session.call_state = 'ended'
        call_session.save()
        
        # Verify final state
        call_session.refresh_from_db()
        self.assertEqual(call_session.call_state, 'ended')
        self.assertIsNotNone(call_session.updated_at)
