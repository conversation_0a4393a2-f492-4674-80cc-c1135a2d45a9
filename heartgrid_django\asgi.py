
"""
ASGI config for heartgrid_django project with Channels support.
"""

import os
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'heartgrid_django.settings')

import django
django.setup()

from channels.routing import ProtocolTypeRouter, URLRouter
from channels.auth import AuthMiddlewareStack
from django.core.asgi import get_asgi_application
import communications.routing


application = ProtocolTypeRouter({
	"http": get_asgi_application(),
	"websocket": AuthMiddlewareStack(
		URLRouter(
			communications.routing.websocket_urlpatterns
		)
	),
})

