"""
Context processors for HeartGrid Communications
Provides WebRTC configuration and other communication-related context to templates
"""

from .webrtc_config import get_webrtc_context
import json


def webrtc_config(request):
    """
    Add WebRTC configuration to template context
    """
    context = get_webrtc_context()
    
    # Convert config to JSON for JavaScript consumption
    context['webrtc_config_json'] = json.dumps(context['webrtc_config'])
    context['media_constraints_json'] = json.dumps(context['media_constraints'])
    
    return context


def communications_config(request):
    """
    Add general communications configuration to template context
    """
    return {
        'communications_enabled': True,
        'webrtc_calling_enabled': True,
        'notifications_enabled': True,
    }
