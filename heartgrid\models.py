"""
HeartGrid Django Models

This module contains all the database models for the HeartGrid dating platform.
Converted from Flask in-memory DataStore to Django ORM models.
"""

import uuid
from datetime import datetime, timedelta
from django.db import models
from django.contrib.auth.models import AbstractUser, BaseUserManager
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone
from PIL import Image
import os


class UserManager(BaseUserManager):
    """
    Custom user manager for email-based authentication
    """
    def create_user(self, email, name, password=None, **extra_fields):
        if not email:
            raise ValueError('The Email field must be set')
        email = self.normalize_email(email)
        user = self.model(email=email, name=name, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user

    def create_superuser(self, email, name, password=None, **extra_fields):
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)

        if extra_fields.get('is_staff') is not True:
            raise ValueError('Superuser must have is_staff=True.')
        if extra_fields.get('is_superuser') is not True:
            raise ValueError('Superuser must have is_superuser=True.')

        return self.create_user(email, name, password, **extra_fields)


class User(AbstractUser):
    """
    Custom User model extending Django's AbstractUser
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    email = models.EmailField(unique=True)
    name = models.CharField(max_length=100)
    date_of_birth = models.DateField(null=True, blank=True)
    age = models.PositiveIntegerField(null=True, blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # OAuth fields
    auth_method = models.CharField(max_length=20, default='email', choices=[
        ('email', 'Email'),
        ('google', 'Google'),
        ('facebook', 'Facebook'),
    ])

    # Remove username field since we're using email
    username = None

    # Fix reverse accessor conflicts
    groups = models.ManyToManyField(
        'auth.Group',
        verbose_name='groups',
        blank=True,
        help_text='The groups this user belongs to.',
        related_name='heartgrid_users',
        related_query_name='heartgrid_user',
    )
    user_permissions = models.ManyToManyField(
        'auth.Permission',
        verbose_name='user permissions',
        blank=True,
        help_text='Specific permissions for this user.',
        related_name='heartgrid_users',
        related_query_name='heartgrid_user',
    )

    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['name']

    # Use custom manager
    objects = UserManager()

    class Meta:
        db_table = 'heartgrid_users'

    def __str__(self):
        return f"{self.name} ({self.email})"

    def save(self, *args, **kwargs):
        # Calculate age from date of birth
        if self.date_of_birth:
            today = timezone.now().date()
            self.age = today.year - self.date_of_birth.year - (
                (today.month, today.day) < (self.date_of_birth.month, self.date_of_birth.day)
            )
        super().save(*args, **kwargs)


class Profile(models.Model):
    """
    User profile model containing dating-specific information
    """
    GENDER_CHOICES = [
        ('male', 'Male'),
        ('female', 'Female'),
        ('non_binary', 'Non-binary'),
        ('other', 'Other'),
    ]

    INTERESTED_IN_CHOICES = [
        ('male', 'Men'),
        ('female', 'Women'),
        ('everyone', 'Everyone'),
    ]

    # Comprehensive list of countries
    COUNTRY_CHOICES = [
        ('US', 'United States'),
        ('CA', 'Canada'),
        ('GB', 'United Kingdom'),
        ('AU', 'Australia'),
        ('DE', 'Germany'),
        ('FR', 'France'),
        ('IT', 'Italy'),
        ('ES', 'Spain'),
        ('NL', 'Netherlands'),
        ('SE', 'Sweden'),
        ('NO', 'Norway'),
        ('DK', 'Denmark'),
        ('FI', 'Finland'),
        ('CH', 'Switzerland'),
        ('AT', 'Austria'),
        ('BE', 'Belgium'),
        ('IE', 'Ireland'),
        ('PT', 'Portugal'),
        ('GR', 'Greece'),
        ('PL', 'Poland'),
        ('CZ', 'Czech Republic'),
        ('HU', 'Hungary'),
        ('RO', 'Romania'),
        ('BG', 'Bulgaria'),
        ('HR', 'Croatia'),
        ('SI', 'Slovenia'),
        ('SK', 'Slovakia'),
        ('LT', 'Lithuania'),
        ('LV', 'Latvia'),
        ('EE', 'Estonia'),
        ('JP', 'Japan'),
        ('KR', 'South Korea'),
        ('CN', 'China'),
        ('IN', 'India'),
        ('SG', 'Singapore'),
        ('HK', 'Hong Kong'),
        ('TW', 'Taiwan'),
        ('TH', 'Thailand'),
        ('MY', 'Malaysia'),
        ('PH', 'Philippines'),
        ('ID', 'Indonesia'),
        ('VN', 'Vietnam'),
        ('NZ', 'New Zealand'),
        ('ZA', 'South Africa'),
        ('BR', 'Brazil'),
        ('MX', 'Mexico'),
        ('AR', 'Argentina'),
        ('CL', 'Chile'),
        ('CO', 'Colombia'),
        ('PE', 'Peru'),
        ('UY', 'Uruguay'),
        ('EC', 'Ecuador'),
        ('VE', 'Venezuela'),
        ('OTHER', 'Other'),
    ]

    # Comprehensive list of activities/interests
    INTEREST_CHOICES = [
        'Sports', 'Fitness', 'Gym', 'Running', 'Cycling', 'Swimming', 'Yoga', 'Hiking',
        'Rock Climbing', 'Skiing', 'Snowboarding', 'Surfing', 'Tennis', 'Golf', 'Basketball',
        'Football', 'Soccer', 'Baseball', 'Volleyball', 'Martial Arts', 'Boxing', 'Dancing',
        'Music', 'Playing Guitar', 'Playing Piano', 'Singing', 'Concerts', 'Festivals',
        'Classical Music', 'Jazz', 'Rock', 'Pop', 'Electronic Music', 'Hip Hop', 'Country',
        'Travel', 'Backpacking', 'Road Trips', 'Beach Vacations', 'City Breaks', 'Adventure Travel',
        'Cultural Tourism', 'Food Tourism', 'Photography', 'Nature Photography', 'Street Photography',
        'Cooking', 'Baking', 'Fine Dining', 'Wine Tasting', 'Craft Beer', 'Coffee', 'Vegetarian',
        'Vegan', 'Foodie', 'BBQ', 'International Cuisine', 'Reading', 'Writing', 'Poetry',
        'Blogging', 'Journalism', 'Literature', 'Science Fiction', 'Mystery', 'Romance',
        'Movies', 'Cinema', 'Netflix', 'Documentaries', 'Horror Movies', 'Comedy', 'Action Movies',
        'Art', 'Painting', 'Drawing', 'Sculpture', 'Museums', 'Galleries', 'Street Art',
        'Gaming', 'Video Games', 'Board Games', 'Card Games', 'Puzzles', 'Chess', 'Poker',
        'Outdoor Activities', 'Camping', 'Fishing', 'Hunting', 'Gardening', 'Bird Watching',
        'Technology', 'Programming', 'AI', 'Startups', 'Entrepreneurship', 'Innovation',
        'Science', 'Astronomy', 'Physics', 'Biology', 'Chemistry', 'Environment', 'Sustainability',
        'Volunteering', 'Charity Work', 'Community Service', 'Animal Welfare', 'Social Causes',
        'Fashion', 'Shopping', 'Beauty', 'Makeup', 'Skincare', 'Style', 'Vintage',
        'Spirituality', 'Meditation', 'Mindfulness', 'Religion', 'Philosophy', 'Psychology',
        'Learning', 'Languages', 'History', 'Politics', 'Economics', 'Business', 'Investing',
        'Pets', 'Dogs', 'Cats', 'Animals', 'Horses', 'Exotic Pets', 'Pet Training',
        'DIY', 'Crafts', 'Woodworking', 'Home Improvement', 'Interior Design', 'Architecture',
        'Cars', 'Motorcycles', 'Racing', 'Mechanics', 'Classic Cars', 'Electric Vehicles',
        'Comedy', 'Stand-up', 'Improv', 'Theater', 'Acting', 'Drama', 'Musical Theater',
        'Nightlife', 'Bars', 'Clubs', 'Parties', 'Social Events', 'Networking', 'Karaoke',
    ]

    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='profile')
    bio = models.TextField(max_length=500, blank=True)
    gender = models.CharField(max_length=20, choices=GENDER_CHOICES, blank=True)
    interested_in = models.CharField(max_length=20, choices=INTERESTED_IN_CHOICES, blank=True)

    # Enhanced location fields
    country = models.CharField(max_length=10, choices=COUNTRY_CHOICES, blank=True)
    city = models.CharField(max_length=100, blank=True)
    location = models.CharField(max_length=100, blank=True)  # Keep for backward compatibility

    # Enhanced matching preferences
    preferred_genders = models.JSONField(default=list, blank=True, help_text="List of preferred genders for matching")
    min_age_preference = models.PositiveIntegerField(null=True, blank=True, help_text="Minimum age preference for matches")
    max_age_preference = models.PositiveIntegerField(null=True, blank=True, help_text="Maximum age preference for matches")
    location_radius = models.PositiveIntegerField(default=50, help_text="Preferred location radius in kilometers")

    # Enhanced interests
    interests = models.JSONField(default=list, blank=True)

    # Profile completion and visibility
    is_complete = models.BooleanField(default=False)
    is_visible = models.BooleanField(default=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'heartgrid_profiles'

    def __str__(self):
        return f"{self.user.name}'s Profile"

    def save(self, *args, **kwargs):
        # Check if profile is complete
        self.is_complete = bool(
            self.bio and self.gender and self.country and self.city and
            self.preferred_genders and self.min_age_preference and self.max_age_preference and
            self.interests and self.photos.exists()
        )
        super().save(*args, **kwargs)

    def get_compatibility_score(self, other_profile):
        """
        Calculate compatibility score with another profile (0-100)
        """
        if not other_profile:
            return 0

        score = 0
        max_score = 0

        # Age preference compatibility (30 points)
        max_score += 30
        if (self.min_age_preference and self.max_age_preference and
            other_profile.user.age and
            self.min_age_preference <= other_profile.user.age <= self.max_age_preference):
            score += 15
        if (other_profile.min_age_preference and other_profile.max_age_preference and
            self.user.age and
            other_profile.min_age_preference <= self.user.age <= other_profile.max_age_preference):
            score += 15

        # Gender preference compatibility (25 points)
        max_score += 25
        if self.gender in other_profile.preferred_genders or 'everyone' in other_profile.preferred_genders:
            score += 12.5
        if other_profile.gender in self.preferred_genders or 'everyone' in self.preferred_genders:
            score += 12.5

        # Shared interests (25 points)
        max_score += 25
        if self.interests and other_profile.interests:
            shared_interests = set(self.interests) & set(other_profile.interests)
            total_interests = set(self.interests) | set(other_profile.interests)
            if total_interests:
                interest_ratio = len(shared_interests) / len(total_interests)
                score += interest_ratio * 25

        # Location proximity (20 points)
        max_score += 20
        if self.country == other_profile.country:
            score += 10
            if self.city and other_profile.city and self.city.lower() == other_profile.city.lower():
                score += 10

        return int((score / max_score) * 100) if max_score > 0 else 0

    def get_shared_interests(self, other_profile):
        """
        Get list of shared interests with another profile
        """
        if not other_profile or not self.interests or not other_profile.interests:
            return []
        return list(set(self.interests) & set(other_profile.interests))


class Photo(models.Model):
    """
    User photos model
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    profile = models.ForeignKey(Profile, on_delete=models.CASCADE, related_name='photos')
    image = models.ImageField(upload_to='profile_photos/')
    is_primary = models.BooleanField(default=False)
    order = models.PositiveIntegerField(default=0)
    uploaded_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'heartgrid_photos'
        ordering = ['order', 'uploaded_at']

    def __str__(self):
        return f"Photo for {self.profile.user.name}"

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)

        # Resize image
        if self.image:
            img = Image.open(self.image.path)
            if img.height > 800 or img.width > 800:
                output_size = (800, 800)
                img.thumbnail(output_size)
                img.save(self.image.path)


class Like(models.Model):
    """
    User likes model
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    liker = models.ForeignKey(User, on_delete=models.CASCADE, related_name='likes_given')
    liked = models.ForeignKey(User, on_delete=models.CASCADE, related_name='likes_received')
    is_super_like = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'heartgrid_likes'
        unique_together = ['liker', 'liked']

    def __str__(self):
        return f"{self.liker.name} likes {self.liked.name}"


class Match(models.Model):
    """
    Mutual matches between users
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user1 = models.ForeignKey(User, on_delete=models.CASCADE, related_name='matches_as_user1')
    user2 = models.ForeignKey(User, on_delete=models.CASCADE, related_name='matches_as_user2')
    created_at = models.DateTimeField(auto_now_add=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        db_table = 'heartgrid_matches'
        unique_together = ['user1', 'user2']

    def __str__(self):
        return f"Match: {self.user1.name} & {self.user2.name}"

    def get_other_user(self, user):
        """Get the other user in the match"""
        return self.user2 if self.user1 == user else self.user1


class Message(models.Model):
    """
    Messages between matched users
    """
    MESSAGE_TYPES = [
        ('text', 'Text'),
        ('image', 'Image'),
        ('voice', 'Voice'),
        ('video', 'Video'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    match = models.ForeignKey(Match, on_delete=models.CASCADE, related_name='messages')
    sender = models.ForeignKey(User, on_delete=models.CASCADE, related_name='sent_messages')
    receiver = models.ForeignKey(User, on_delete=models.CASCADE, related_name='received_messages')

    message_type = models.CharField(max_length=10, choices=MESSAGE_TYPES, default='text')
    content = models.TextField(blank=True)  # Text content
    media_file = models.FileField(upload_to='message_media/', blank=True, null=True)

    # Message status
    is_read = models.BooleanField(default=False)
    is_deleted = models.BooleanField(default=False)

    # Reactions
    reactions = models.JSONField(default=dict, blank=True)  # {user_id: reaction_emoji}

    created_at = models.DateTimeField(auto_now_add=True)
    read_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        db_table = 'heartgrid_messages'
        ordering = ['created_at']

    def __str__(self):
        return f"Message from {self.sender.name} to {self.receiver.name}"


class Subscription(models.Model):
    """
    User subscription model
    """
    PLAN_CHOICES = [
        ('trial', 'Trial'),
        ('weekly', 'Weekly'),
        ('fortnightly', 'Fortnightly'),
        ('monthly', 'Monthly'),
    ]

    STATUS_CHOICES = [
        ('active', 'Active'),
        ('expired', 'Expired'),
        ('cancelled', 'Cancelled'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='subscription')
    plan = models.CharField(max_length=20, choices=PLAN_CHOICES)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active')
    features = models.JSONField(default=list)  # List of available features

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    expires_at = models.DateTimeField()

    class Meta:
        db_table = 'heartgrid_subscriptions'

    def __str__(self):
        return f"{self.user.name} - {self.plan} ({self.status})"

    def is_active(self):
        """Check if subscription is currently active"""
        return self.status == 'active' and self.expires_at > timezone.now()

    def has_feature(self, feature_name):
        """Check if subscription includes a specific feature"""
        return feature_name in self.features

    def save(self, *args, **kwargs):
        # Auto-expire subscription if past expiry date
        if self.expires_at and self.expires_at <= timezone.now() and self.status == 'active':
            self.status = 'expired'
        super().save(*args, **kwargs)


class CryptoPayment(models.Model):
    """
    Cryptocurrency payment tracking
    """
    CHAIN_CHOICES = [
        ('ethereum', 'Ethereum'),
        ('bsc', 'BNB Smart Chain'),
        ('solana', 'Solana'),
        ('tron', 'Tron'),
        ('ton', 'TON'),
    ]

    TOKEN_CHOICES = [
        ('native', 'Native Token'),
        ('usdt', 'USDT'),
    ]

    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('confirmed', 'Confirmed'),
        ('failed', 'Failed'),
        ('expired', 'Expired'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='crypto_payments')
    subscription_plan = models.CharField(max_length=20)

    # Payment details
    chain = models.CharField(max_length=20, choices=CHAIN_CHOICES)
    token_type = models.CharField(max_length=10, choices=TOKEN_CHOICES)
    payment_address = models.CharField(max_length=100)
    amount_crypto = models.DecimalField(max_digits=20, decimal_places=8)
    amount_usd = models.DecimalField(max_digits=10, decimal_places=2)

    # Transaction details
    tx_hash = models.CharField(max_length=100, blank=True, null=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')

    created_at = models.DateTimeField(auto_now_add=True)
    confirmed_at = models.DateTimeField(null=True, blank=True)
    expires_at = models.DateTimeField()

    class Meta:
        db_table = 'heartgrid_crypto_payments'

    def __str__(self):
        return f"Payment {self.id} - {self.user.name} ({self.status})"


class AdminUser(models.Model):
    """
    Admin users model
    """
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='admin_profile')
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'heartgrid_admin_users'

    def __str__(self):
        return f"Admin: {self.user.name}"


class Notification(models.Model):
    """
    User notifications model
    """
    NOTIFICATION_TYPES = [
        ('like', 'Like'),
        ('super_like', 'Super Like'),
        ('match', 'Match'),
        ('message', 'Message'),
        ('subscription', 'Subscription'),
        ('system', 'System'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='notifications')
    notification_type = models.CharField(max_length=20, choices=NOTIFICATION_TYPES)
    title = models.CharField(max_length=100)
    message = models.TextField()

    # Related objects
    related_user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True, related_name='notifications_about')
    related_match = models.ForeignKey(Match, on_delete=models.CASCADE, null=True, blank=True)

    is_read = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    read_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        db_table = 'heartgrid_notifications'
        ordering = ['-created_at']

    def __str__(self):
        return f"Notification for {self.user.name}: {self.title}"


class UserActivity(models.Model):
    """
    Track user activities for gamification and analytics
    """
    ACTIVITY_TYPES = [
        ('login', 'Login'),
        ('like_sent', 'Like Sent'),
        ('super_like_sent', 'Super Like Sent'),
        ('match_created', 'Match Created'),
        ('message_sent', 'Message Sent'),
        ('profile_updated', 'Profile Updated'),
        ('photo_uploaded', 'Photo Uploaded'),
        ('subscription_created', 'Subscription Created'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='activities')
    activity_type = models.CharField(max_length=30, choices=ACTIVITY_TYPES)
    metadata = models.JSONField(default=dict, blank=True)  # Additional activity data
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'heartgrid_user_activities'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.user.name} - {self.activity_type}"


class Achievement(models.Model):
    """
    User achievements for gamification
    """
    ACHIEVEMENT_TYPES = [
        ('first_like', 'First Like'),
        ('first_match', 'First Match'),
        ('first_message', 'First Message'),
        ('profile_complete', 'Profile Complete'),
        ('photo_verified', 'Photo Verified'),
        ('early_adopter', 'Early Adopter'),
        ('social_butterfly', 'Social Butterfly'),
        ('conversation_starter', 'Conversation Starter'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='achievements')
    achievement_type = models.CharField(max_length=30, choices=ACHIEVEMENT_TYPES)
    title = models.CharField(max_length=100)
    description = models.TextField()
    icon = models.CharField(max_length=50, default='🏆')  # Emoji or icon class
    earned_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'heartgrid_achievements'
        unique_together = ['user', 'achievement_type']
        ordering = ['-earned_at']

    def __str__(self):
        return f"{self.user.name} - {self.title}"


class ReferralLink(models.Model):
    """
    Referral program invite links
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    inviter = models.ForeignKey(User, on_delete=models.CASCADE, related_name='referral_links')
    code = models.CharField(max_length=32, unique=True)
    created_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField(null=True, blank=True)  # null means never expires
    max_uses = models.PositiveIntegerField(default=100, null=True, blank=True)  # null means unlimited
    uses = models.PositiveIntegerField(default=0)
    is_active = models.BooleanField(default=True)
    earning_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=10.00, help_text="Earning percentage per subscription (e.g. 10.00 for 10%)")

    class Meta:
        db_table = 'heartgrid_referral_links'

    def __str__(self):
        return f"ReferralLink {self.code} by {self.inviter.name}"

    def is_valid(self):
        not_expired = self.expires_at is None or self.expires_at > timezone.now()
        under_max_uses = self.max_uses is None or self.uses < self.max_uses
        return self.is_active and not_expired and under_max_uses


class ReferralEarning(models.Model):
    """
    Tracks earnings from referral subscriptions
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    referral_link = models.ForeignKey(ReferralLink, on_delete=models.CASCADE, related_name='earnings')
    referred_user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='referral_earnings')
    subscription = models.ForeignKey('Subscription', on_delete=models.CASCADE, related_name='referral_earnings')
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    created_at = models.DateTimeField(auto_now_add=True)
    paid_out = models.BooleanField(default=False)
    paid_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        db_table = 'heartgrid_referral_earnings'

    def __str__(self):
        return f"Earning ${self.amount} for {self.referral_link.inviter.name} from {self.referred_user.name}"


class PaymentRequest(models.Model):
    """
    Payment requests for referral earnings
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    inviter = models.ForeignKey(User, on_delete=models.CASCADE, related_name='payment_requests')
    amount_requested = models.DecimalField(max_digits=10, decimal_places=2)
    status = models.CharField(max_length=20, choices=[('pending','Pending'),('approved','Approved'),('paid','Paid'),('rejected','Rejected')], default='pending')
    created_at = models.DateTimeField(auto_now_add=True)
    processed_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        db_table = 'heartgrid_payment_requests'

    def __str__(self):
        return f"PaymentRequest ${self.amount_requested} by {self.inviter.name} ({self.status})"


class PaymentHistory(models.Model):
    """
    Payment history for referral payouts
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    inviter = models.ForeignKey(User, on_delete=models.CASCADE, related_name='payment_history')
    amount_paid = models.DecimalField(max_digits=10, decimal_places=2)
    payment_request = models.ForeignKey(PaymentRequest, on_delete=models.SET_NULL, null=True, blank=True, related_name='payments')
    paid_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'heartgrid_payment_history'

    def __str__(self):
        return f"PaymentHistory ${self.amount_paid} to {self.inviter.name}"
