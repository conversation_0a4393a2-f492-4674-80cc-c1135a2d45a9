"""
HeartGrid Django Views

This module contains all the API views for the HeartGrid dating platform.
Converted from Flask routes to Django REST Framework views.
"""

from rest_framework import status, generics, permissions, viewsets
from rest_framework.decorators import api_view, permission_classes, action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.authtoken.models import Token
from django.contrib.auth import login, logout
from django.shortcuts import get_object_or_404
from django.db.models import Q
from django.utils import timezone
from datetime import datetime, timedelta
import uuid

from .models import (
    User, Profile, Photo, Like, Match, Message,
    Subscription, CryptoPayment, Notification,
    UserActivity, Achievement, ReferralLink
)
from .serializers import (
    UserRegistrationSerializer, UserLoginSerializer, UserSerializer,
    ProfileSerializer, PhotoSerializer, LikeSerializer, MatchSerializer,
    MessageSerializer, SubscriptionSerializer, NotificationSerializer,
    AchievementSerializer, UserActivitySerializer, CryptoPaymentSerializer
)

# Additional imports for frontend views
from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
from django.contrib.auth import authenticate, login as auth_login, logout as auth_logout
from django.contrib import messages
from django.views.decorators.csrf import csrf_protect
from django.core.exceptions import ValidationError
from django.contrib.auth.password_validation import validate_password

# ============================================================================
# FRONTEND VIEWS (Template-based)
# ============================================================================

def index(request):
    """Landing page view"""
    return render(request, 'index.html')

@csrf_protect
def login_page(request):
    """Login page view with form handling"""
    if request.user.is_authenticated:
        return redirect('heartgrid_frontend:discover_page')

    if request.method == 'POST':
        email = request.POST.get('email', '').strip().lower()
        password = request.POST.get('password', '')

        if not email or not password:
            messages.error(request, 'Email and password are required.')
            return render(request, 'login.html')

        # Authenticate user
        user = authenticate(request, username=email, password=password)
        if user is not None:
            if user.is_active:
                auth_login(request, user, backend='django.contrib.auth.backends.ModelBackend')
                messages.success(request, f'Welcome back, {user.first_name or user.email}!')
                return redirect('heartgrid_frontend:discover_page')
            else:
                messages.error(request, 'Your account has been deactivated.')
        else:
            messages.error(request, 'Invalid email or password.')

    return render(request, 'login.html')

@csrf_protect
def generate_referral_link(request):
    """Generate a unique referral link for the current user"""
    if not request.user.is_authenticated:
        return redirect('heartgrid_frontend:login_page')
    from django.utils.crypto import get_random_string
    code = get_random_string(16)
    expires_at = datetime(2024, 8, 31, 23, 59, 59)
    link = ReferralLink.objects.create(
        inviter=request.user,
        code=code,
        expires_at=expires_at,
        max_uses=100
    )
    referral_url = request.build_absolute_uri(f"/register/?ref={code}")
    messages.success(request, f"Your referral link: {referral_url}")
    return render(request, 'referral_link.html', {'referral_url': referral_url})

@csrf_protect
def register_page(request):
    """Registration page view with form handling and referral support"""
    if request.user.is_authenticated:
        return redirect('heartgrid_frontend:discover_page')
    ref_code = request.GET.get('ref')
    if request.method == 'POST':
        name = request.POST.get('name', '').strip()
        email = request.POST.get('email', '').strip().lower()
        date_of_birth = request.POST.get('date_of_birth', '')
        password = request.POST.get('password', '')

        # Validation
        if not all([name, email, date_of_birth, password]):
            messages.error(request, 'All fields are required.')
            return render(request, 'register.html')

        # Validate name length
        if len(name) < 2 or len(name) > 50:
            messages.error(request, 'Name must be between 2 and 50 characters.')
            return render(request, 'register.html')

        # Check if user already exists
        if User.objects.filter(email=email).exists():
            messages.error(request, 'An account with this email already exists.')
            return render(request, 'register.html')

        # Validate password
        try:
            validate_password(password)
        except ValidationError as e:
            messages.error(request, ' '.join(e.messages))
            return render(request, 'register.html')

        try:
            # Parse date of birth and calculate age
            from datetime import datetime
            try:
                dob = datetime.strptime(date_of_birth, '%Y-%m-%d').date()
                today = datetime.now().date()
                age = today.year - dob.year - ((today.month, today.day) < (dob.month, dob.day))
            except ValueError:
                messages.error(request, 'Invalid date format. Please use YYYY-MM-DD.')
                return render(request, 'register.html')
            user = User.objects.create_user(
                email=email,
                name=name,
                password=password,
                date_of_birth=dob,
                age=age
            )
        except Exception as e:
            messages.error(request, f'Error creating user: {str(e)}')
            return render(request, 'register.html')
        profile, created = Profile.objects.get_or_create(
            user=user,
            defaults={
                'bio': '',
                'location': '',
                'interests': [],
                'is_complete': False,
                'is_visible': True
            }
        )
        # Referral logic
        if ref_code:
            try:
                referral = ReferralLink.objects.get(code=ref_code)
                if referral.is_valid():
                    # Grant full access until August 31
                    expires_at = datetime(2024, 8, 31, 23, 59, 59)
                    Subscription.objects.create(
                        user=user,
                        plan='trial',
                        status='active',
                        expires_at=expires_at,
                        features=[
                            'basic_chat', 'super_likes', 'read_receipts', 'voice_messages',
                            'video_calls', 'priority_matching'
                        ]
                    )
                    referral.uses += 1
                    referral.save()
                else:
                    messages.error(request, 'Referral link is invalid or expired.')
                    return render(request, 'register.html')
            except ReferralLink.DoesNotExist:
                messages.error(request, 'Referral link not found.')
                return render(request, 'register.html')
        else:
            # Default trial subscription
            trial_end = timezone.now() + timedelta(days=3)
            Subscription.objects.create(
                user=user,
                plan='trial',
                status='active',
                expires_at=trial_end,
                features=['basic_chat']
            )
        auth_login(request, user, backend='django.contrib.auth.backends.ModelBackend')
        messages.success(request, f'Welcome to HeartGrid, {user.name}!')
        return redirect('heartgrid_frontend:profile_page')
    return render(request, 'register.html')

def logout_page(request):
    """Logout view"""
    if request.user.is_authenticated:
        auth_logout(request)
        messages.success(request, 'You have been successfully logged out.')
    return redirect('heartgrid_frontend:index')

@login_required
def discover_page(request):
    """Discover page view"""
    return render(request, 'discover_modern.html')

@login_required
def profile_page(request):
    """Profile page view"""
    return render(request, 'profile.html')

@login_required
def matches_page(request):
    """Matches page view"""
    return render(request, 'matches.html')

@login_required
def chat_page(request, match_user_id=None):
    """Chat page view"""
    context = {'match_user_id': match_user_id} if match_user_id else {}
    return render(request, 'chat.html', context)

@login_required
def subscription_page(request):
    """Subscription page view"""
    from .models import Subscription

    context = {}

    # Get user's current subscription
    try:
        subscription = Subscription.objects.get(user=request.user)
        context['subscription'] = subscription
        context['can_chat'] = subscription.is_active()
    except Subscription.DoesNotExist:
        context['subscription'] = None
        context['can_chat'] = False

    return render(request, 'subscription.html', context)

@login_required
def notifications_page(request):
    """Notifications page view"""
    from .models import Notification

    # Get user's notifications
    notifications = Notification.objects.filter(user=request.user).order_by('-created_at')[:50]

    context = {
        'notifications': notifications,
        'unread_count': notifications.filter(read=False).count()
    }

    return render(request, 'notifications.html', context)

@login_required
def gamification_page(request):
    """Gamification page view"""
    from .gamification import gamification_engine
    from .models import Achievement, UserActivity

    # Get gamification data for the user
    gamification_data = gamification_engine.get_user_stats(request.user)

    # Calculate level progress
    current_level = gamification_data.get('level', 1)
    total_points = gamification_data.get('total_points', 0)
    current_level_points = (current_level - 1) * 100
    next_level_points = current_level * 100
    points_to_next = next_level_points - total_points
    level_progress = max(0, min(100, ((total_points - current_level_points) / (next_level_points - current_level_points)) * 100))

    # Add calculated values to gamification data
    gamification_data['level_progress'] = level_progress
    gamification_data['points_to_next_level'] = max(0, points_to_next)

    # Get daily challenge (mock data for now)
    daily_challenge = {
        'day': 'Monday',
        'task': 'Send Likes',  # Already formatted for display
        'target': 5,
        'progress': 2,
        'reward': 'Super Like',
        'progress_percent': (2 / 5) * 100  # Calculate progress percentage
    }

    # Get leaderboard data
    leaderboard = gamification_engine.get_leaderboard()

    context = {
        'gamification_data': gamification_data,
        'daily_challenge': daily_challenge,
        'leaderboard': leaderboard
    }

    return render(request, 'gamification.html', context)

# ============================================================================
# API VIEWS (REST API)
# ============================================================================

# Authentication Views
class UserRegistrationView(generics.CreateAPIView):
    """
    User registration endpoint
    """
    serializer_class = UserRegistrationSerializer
    permission_classes = [AllowAny]

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        user = serializer.save()

        # Create auth token
        token, created = Token.objects.get_or_create(user=user)

        # Log user activity
        UserActivity.objects.create(
            user=user,
            activity_type='login',
            metadata={'registration': True}
        )

        return Response({
            'user': UserSerializer(user).data,
            'token': token.key,
            'message': 'Welcome to HeartGrid! Please complete your profile.'
        }, status=status.HTTP_201_CREATED)


@api_view(['POST'])
@permission_classes([AllowAny])
def user_login(request):
    """
    User login endpoint
    """
    serializer = UserLoginSerializer(data=request.data)
    if serializer.is_valid():
        user = serializer.validated_data['user']

        # Create or get auth token
        token, created = Token.objects.get_or_create(user=user)

        # Log user activity
        UserActivity.objects.create(
            user=user,
            activity_type='login',
            metadata={'login_time': timezone.now().isoformat()}
        )

        return Response({
            'user': UserSerializer(user).data,
            'token': token.key,
            'message': f'Welcome back, {user.name}!'
        })

    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def user_logout(request):
    """
    User logout endpoint
    """
    try:
        # Delete the user's token
        request.user.auth_token.delete()
        return Response({'message': 'Successfully logged out.'})
    except:
        return Response({'message': 'Logout successful.'})


# Profile Views
class ProfileViewSet(viewsets.ModelViewSet):
    """
    ViewSet for user profiles with enhanced field handling
    """
    serializer_class = ProfileSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return Profile.objects.filter(user=self.request.user)

    def get_object(self):
        return get_object_or_404(Profile, user=self.request.user)

    def update(self, request, *args, **kwargs):
        # Handle special field processing
        data = request.data.copy()

        # Ensure preferred_genders is a list
        if 'preferred_genders' in data:
            if isinstance(data['preferred_genders'], str):
                data['preferred_genders'] = [data['preferred_genders']]
            elif not isinstance(data['preferred_genders'], list):
                data['preferred_genders'] = list(data['preferred_genders'])

        # Ensure interests is a list
        if 'interests' in data:
            if isinstance(data['interests'], str):
                data['interests'] = [data['interests']]
            elif not isinstance(data['interests'], list):
                data['interests'] = list(data['interests'])

        # Convert age preferences to integers
        for field in ['min_age_preference', 'max_age_preference', 'location_radius']:
            if field in data and data[field]:
                try:
                    data[field] = int(data[field])
                except (ValueError, TypeError):
                    pass

        # Update request data
        request._full_data = data

        response = super().update(request, *args, **kwargs)

        # Log profile update activity with enhanced metadata
        updated_fields = list(data.keys())
        profile = self.get_object()

        UserActivity.objects.create(
            user=request.user,
            activity_type='profile_updated',
            metadata={
                'updated_fields': updated_fields,
                'profile_completion': profile.is_complete,
                'has_preferences': bool(profile.preferred_genders and
                                      profile.min_age_preference and
                                      profile.max_age_preference),
                'interests_count': len(profile.interests) if profile.interests else 0
            }
        )

        return response

    def retrieve(self, request, *args, **kwargs):
        """Enhanced retrieve with additional context"""
        response = super().retrieve(request, *args, **kwargs)

        profile = self.get_object()

        # Add profile completion details
        completion_details = {
            'required_fields': {
                'basic_info': bool(profile.gender and profile.user.age),
                'location': bool(profile.country and profile.city),
                'preferences': bool(profile.preferred_genders and
                                 profile.min_age_preference and
                                 profile.max_age_preference),
                'bio': bool(profile.bio and len(profile.bio.strip()) >= 50),
                'interests': bool(profile.interests and len(profile.interests) >= 3),
                'photos': profile.photos.exists()
            }
        }

        completion_details['completion_percentage'] = (
            sum(completion_details['required_fields'].values()) /
            len(completion_details['required_fields']) * 100
        )

        response.data['completion_details'] = completion_details

        return response


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def upload_photo(request):
    """
    Upload profile photo endpoint
    """
    if 'photo' not in request.FILES:
        return Response({'error': 'No photo provided'}, status=status.HTTP_400_BAD_REQUEST)

    profile = get_object_or_404(Profile, user=request.user)

    # Create photo instance
    photo = Photo.objects.create(
        profile=profile,
        image=request.FILES['photo'],
        is_primary=not profile.photos.exists()  # First photo is primary
    )

    # Log photo upload activity
    UserActivity.objects.create(
        user=request.user,
        activity_type='photo_uploaded',
        metadata={'photo_id': str(photo.id)}
    )

    return Response({
        'photo': PhotoSerializer(photo).data,
        'message': 'Photo uploaded successfully!'
    })


@api_view(['DELETE'])
@permission_classes([IsAuthenticated])
def delete_photo(request, photo_id):
    """
    Delete profile photo endpoint
    """
    photo = get_object_or_404(Photo, id=photo_id, profile__user=request.user)
    photo.delete()

    return Response({'message': 'Photo deleted successfully!'})


# Discovery and Matching Views
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def discover_profiles(request):
    """
    Get discoverable profiles for the current user with enhanced matching
    """
    user = request.user
    user_profile = get_object_or_404(Profile, user=user)

    # Get users already liked by current user
    liked_user_ids = Like.objects.filter(liker=user).values_list('liked_id', flat=True)

    # Build base query for discoverable profiles
    queryset = Profile.objects.filter(
        is_visible=True,
        is_complete=True
    ).exclude(
        user=user
    ).exclude(
        user_id__in=liked_user_ids
    ).select_related('user').prefetch_related('photos')

    # Enhanced gender filtering using preferred_genders
    if user_profile.preferred_genders:
        # Filter profiles whose gender matches user's preferences
        queryset = queryset.filter(gender__in=user_profile.preferred_genders)

        # Also filter profiles who are interested in user's gender
        # This ensures mutual compatibility
        from django.db.models import Q
        mutual_interest_filter = Q()
        for gender in user_profile.preferred_genders:
            if gender == 'everyone':
                continue
            mutual_interest_filter |= Q(preferred_genders__contains=[user_profile.gender])

        if mutual_interest_filter:
            queryset = queryset.filter(mutual_interest_filter)

    # Age filtering based on preferences
    if user_profile.min_age_preference and user_profile.max_age_preference:
        queryset = queryset.filter(
            user__age__gte=user_profile.min_age_preference,
            user__age__lte=user_profile.max_age_preference
        )

    # Location filtering (if both profiles have location data)
    # This is a simplified version - in production you'd use proper geospatial queries
    if user_profile.city and user_profile.country:
        # Prioritize same city/country matches
        same_location = queryset.filter(
            city__iexact=user_profile.city,
            country=user_profile.country
        )
        different_location = queryset.exclude(
            city__iexact=user_profile.city,
            country=user_profile.country
        )

        # Combine with same location first
        queryset = same_location.union(different_location, all=True)

    # Get profiles and calculate compatibility scores
    limit = int(request.GET.get('limit', 20))
    profiles = list(queryset[:limit * 2])  # Get more to sort by compatibility

    # Calculate compatibility scores and sort
    profiles_with_scores = []
    for profile in profiles:
        compatibility_score = user_profile.get_compatibility_score(profile)
        profiles_with_scores.append((profile, compatibility_score))

    # Sort by compatibility score (highest first)
    profiles_with_scores.sort(key=lambda x: x[1], reverse=True)

    # Take top matches
    top_profiles = [profile for profile, score in profiles_with_scores[:limit]]

    # Serialize with context for compatibility scores
    serializer = ProfileSerializer(
        top_profiles,
        many=True,
        context={'request': request}
    )

    return Response({
        'profiles': serializer.data,
        'count': len(top_profiles),
        'user_preferences': {
            'preferred_genders': user_profile.preferred_genders,
            'age_range': [user_profile.min_age_preference, user_profile.max_age_preference],
            'location': f"{user_profile.city}, {user_profile.get_country_display()}" if user_profile.city and user_profile.country else None,
            'radius': user_profile.location_radius
        }
    })


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def like_profile(request):
    """
    Like a user profile
    """
    liked_user_id = request.data.get('user_id')
    if not liked_user_id:
        return Response({'error': 'User ID required'}, status=status.HTTP_400_BAD_REQUEST)

    try:
        liked_user = User.objects.get(id=liked_user_id)
    except User.DoesNotExist:
        return Response({'error': 'User not found'}, status=status.HTTP_404_NOT_FOUND)

    # Check if already liked
    if Like.objects.filter(liker=request.user, liked=liked_user).exists():
        return Response({'error': 'Already liked this user'}, status=status.HTTP_400_BAD_REQUEST)

    # Create like
    like = Like.objects.create(
        liker=request.user,
        liked=liked_user,
        is_super_like=request.data.get('is_super_like', False)
    )

    # Check for mutual like (match)
    mutual_like = Like.objects.filter(liker=liked_user, liked=request.user).first()
    is_match = bool(mutual_like)
    match_id = None

    if is_match:
        # Create match
        match = Match.objects.create(
            user1=request.user,
            user2=liked_user
        )
        match_id = str(match.id)

        # Log match activities
        UserActivity.objects.create(
            user=request.user,
            activity_type='match_created',
            metadata={'match_id': match_id, 'matched_with': str(liked_user.id)}
        )
        UserActivity.objects.create(
            user=liked_user,
            activity_type='match_created',
            metadata={'match_id': match_id, 'matched_with': str(request.user.id)}
        )

        # Create notifications
        Notification.objects.create(
            user=request.user,
            notification_type='match',
            title='New Match!',
            message=f'You matched with {liked_user.name}',
            related_user=liked_user,
            related_match=match
        )
        Notification.objects.create(
            user=liked_user,
            notification_type='match',
            title='New Match!',
            message=f'You matched with {request.user.name}',
            related_user=request.user,
            related_match=match
        )

    # Log like activity
    activity_type = 'super_like_sent' if like.is_super_like else 'like_sent'
    UserActivity.objects.create(
        user=request.user,
        activity_type=activity_type,
        metadata={'liked_user': str(liked_user.id)}
    )

    # Create notification for liked user
    notification_type = 'super_like' if like.is_super_like else 'like'
    title = 'Someone super liked you!' if like.is_super_like else 'Someone liked you!'
    Notification.objects.create(
        user=liked_user,
        notification_type=notification_type,
        title=title,
        message=f'{request.user.name} {"super " if like.is_super_like else ""}liked you',
        related_user=request.user
    )

    response_data = {
        'success': True,
        'is_match': is_match,
        'is_super_like': like.is_super_like
    }

    if is_match:
        response_data.update({
            'match_name': liked_user.name,
            'match_id': match_id
        })

    return Response(response_data)


# Match and Messaging Views
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_matches(request):
    """
    Get user's matches with enhanced compatibility data
    """
    user_profile = get_object_or_404(Profile, user=request.user)

    # Get filtering parameters
    min_compatibility = int(request.GET.get('min_compatibility', 0))
    sort_by = request.GET.get('sort_by', 'compatibility')  # compatibility, recent, alphabetical

    matches = Match.objects.filter(
        Q(user1=request.user) | Q(user2=request.user),
        is_active=True
    ).select_related('user1', 'user2').prefetch_related(
        'user1__profile__photos', 'user2__profile__photos'
    )

    match_data = []
    for match in matches:
        other_user = match.user2 if match.user1 == request.user else match.user1
        other_profile = other_user.profile

        # Calculate compatibility score and shared interests
        compatibility_score = user_profile.get_compatibility_score(other_profile)
        shared_interests = user_profile.get_shared_interests(other_profile)

        # Apply compatibility filter
        if compatibility_score < min_compatibility:
            continue

        # Get match reasons
        match_reasons = []

        # Age compatibility
        if (user_profile.min_age_preference and user_profile.max_age_preference and
            other_user.age and
            user_profile.min_age_preference <= other_user.age <= user_profile.max_age_preference):
            match_reasons.append("Perfect age match")

        # Location compatibility
        if (user_profile.city and other_profile.city and
            user_profile.city.lower() == other_profile.city.lower()):
            match_reasons.append("Same city")
        elif (user_profile.country and other_profile.country and
              user_profile.country == other_profile.country):
            match_reasons.append("Same country")

        # Shared interests
        if len(shared_interests) >= 5:
            match_reasons.append(f"{len(shared_interests)} shared interests")
        elif len(shared_interests) >= 3:
            match_reasons.append(f"{len(shared_interests)} common interests")

        # Gender preference compatibility
        if (user_profile.preferred_genders and
            other_profile.gender in user_profile.preferred_genders):
            match_reasons.append("Matches your preferences")

        match_data.append({
            'match_id': str(match.id),
            'user': UserSerializer(other_user).data,
            'profile': ProfileSerializer(other_profile, context={'request': request}).data,
            'compatibility_score': compatibility_score,
            'shared_interests': shared_interests,
            'match_reasons': match_reasons,
            'created_at': match.created_at,
            'days_since_match': (timezone.now() - match.created_at).days
        })

    # Sort matches based on preference
    if sort_by == 'compatibility':
        match_data.sort(key=lambda x: x['compatibility_score'], reverse=True)
    elif sort_by == 'recent':
        match_data.sort(key=lambda x: x['created_at'], reverse=True)
    elif sort_by == 'alphabetical':
        match_data.sort(key=lambda x: x['user']['name'])

    # Get summary statistics
    total_matches = len(match_data)
    avg_compatibility = sum(m['compatibility_score'] for m in match_data) / total_matches if total_matches > 0 else 0
    high_compatibility_count = len([m for m in match_data if m['compatibility_score'] >= 80])

    return Response({
        'matches': match_data,
        'summary': {
            'total_matches': total_matches,
            'average_compatibility': round(avg_compatibility, 1),
            'high_compatibility_matches': high_compatibility_count,
            'filters_applied': {
                'min_compatibility': min_compatibility,
                'sort_by': sort_by
            }
        }
    })


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_all_messages(request):
    """
    Get all messages for the authenticated user
    """
    try:
        # Get all messages where user is sender or receiver
        messages = Message.objects.filter(
            Q(sender=request.user) | Q(receiver=request.user)
        ).order_by('-created_at')[:50]  # Latest 50 messages

        message_data = []
        for message in messages:
            message_data.append({
                'id': str(message.id),
                'sender': message.sender.name,
                'receiver': message.receiver.name,
                'content': message.content,
                'created_at': message.created_at.isoformat(),
                'is_read': message.is_read
            })

        return Response({'messages': message_data})
    except Exception as e:
        return Response({'error': str(e)}, status=500)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_conversation(request, match_user_id):
    """
    Get conversation with a matched user
    """
    try:
        match_user = User.objects.get(id=match_user_id)
    except User.DoesNotExist:
        return Response({'error': 'User not found'}, status=status.HTTP_404_NOT_FOUND)

    # Verify they are matched
    match = Match.objects.filter(
        Q(user1=request.user, user2=match_user) | Q(user1=match_user, user2=request.user),
        is_active=True
    ).first()

    if not match:
        return Response({'error': 'Not matched with this user'}, status=status.HTTP_403_FORBIDDEN)

    # Get messages
    messages = Message.objects.filter(
        match=match,
        is_deleted=False
    ).select_related('sender', 'receiver').order_by('created_at')

    return Response({
        'match': MatchSerializer(match).data,
        'messages': MessageSerializer(messages, many=True).data,
        'match_user': UserSerializer(match_user).data
    })


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def send_message(request):
    """
    Send a message to a matched user
    """
    match_user_id = request.data.get('user_id')
    content = request.data.get('content', '').strip()
    message_type = request.data.get('message_type', 'text')

    if not match_user_id:
        return Response({'error': 'User ID required'}, status=status.HTTP_400_BAD_REQUEST)

    if not content and message_type == 'text':
        return Response({'error': 'Message content required'}, status=status.HTTP_400_BAD_REQUEST)

    try:
        match_user = User.objects.get(id=match_user_id)
    except User.DoesNotExist:
        return Response({'error': 'User not found'}, status=status.HTTP_404_NOT_FOUND)

    # Verify they are matched
    match = Match.objects.filter(
        Q(user1=request.user, user2=match_user) | Q(user1=match_user, user2=request.user),
        is_active=True
    ).first()

    if not match:
        return Response({'error': 'Not matched with this user'}, status=status.HTTP_403_FORBIDDEN)

    # Create message
    message = Message.objects.create(
        match=match,
        sender=request.user,
        receiver=match_user,
        message_type=message_type,
        content=content
    )

    # Log message activity
    UserActivity.objects.create(
        user=request.user,
        activity_type='message_sent',
        metadata={'message_id': str(message.id), 'receiver': str(match_user.id)}
    )

    # Create notification for receiver
    Notification.objects.create(
        user=match_user,
        notification_type='message',
        title='New Message',
        message=f'{request.user.name} sent you a message',
        related_user=request.user,
        related_match=match
    )

    return Response({
        'message': MessageSerializer(message).data,
        'success': True
    })


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def mark_message_read(request, message_id):
    """
    Mark a message as read
    """
    try:
        message = Message.objects.get(id=message_id, receiver=request.user)
        if not message.is_read:
            message.is_read = True
            message.read_at = timezone.now()
            message.save()

        return Response({'success': True})
    except Message.DoesNotExist:
        return Response({'error': 'Message not found'}, status=status.HTTP_404_NOT_FOUND)


# Subscription and Payment Views
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_subscription(request):
    """
    Get user's current subscription
    """
    try:
        subscription = Subscription.objects.get(user=request.user)
        return Response({
            'subscription': SubscriptionSerializer(subscription).data,
            'is_active': subscription.is_active()
        })
    except Subscription.DoesNotExist:
        return Response({'error': 'No subscription found'}, status=status.HTTP_404_NOT_FOUND)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def create_crypto_payment(request):
    """
    Create a cryptocurrency payment for subscription
    """
    from .crypto_utils import crypto_handler

    plan = request.data.get('plan')
    chain = request.data.get('chain')
    token_type = request.data.get('token_type', 'native')

    if not plan or not chain:
        return Response({'error': 'Plan and chain required'}, status=status.HTTP_400_BAD_REQUEST)

    try:
        payment = crypto_handler.create_payment_request(
            user=request.user,
            plan=plan,
            chain=chain,
            token_type=token_type
        )

        return Response({
            'payment': CryptoPaymentSerializer(payment).data,
            'supported_chains': list(crypto_handler.SUPPORTED_CHAINS.keys()),
            'chain_info': crypto_handler.SUPPORTED_CHAINS.get(chain, {}),
            'message': f'Payment created. Please send exactly {payment.amount_crypto} {chain.upper()} to {payment.payment_address}'
        })

    except ValueError as e:
        return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        return Response({'error': 'Failed to create payment'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def verify_crypto_payment(request):
    """
    Verify a cryptocurrency payment with transaction hash
    """
    from .crypto_utils import crypto_handler

    payment_id = request.data.get('payment_id')
    tx_hash = request.data.get('tx_hash')

    if not payment_id or not tx_hash:
        return Response({'error': 'Payment ID and transaction hash required'}, status=status.HTTP_400_BAD_REQUEST)

    try:
        success = crypto_handler.verify_payment(payment_id, tx_hash)

        if success:
            return Response({
                'success': True,
                'message': 'Payment verified successfully! Your subscription has been activated.'
            })
        else:
            return Response({
                'success': False,
                'message': 'Payment verification failed. Please check your transaction hash.'
            }, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        return Response({'error': 'Payment verification failed'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_payment_status(request, payment_id):
    """
    Get the status of a cryptocurrency payment
    """
    from .crypto_utils import crypto_handler

    try:
        status_info = crypto_handler.get_payment_status(payment_id)

        if status_info.get('status') == 'not_found':
            return Response({'error': 'Payment not found'}, status=status.HTTP_404_NOT_FOUND)

        return Response(status_info)

    except Exception as e:
        return Response({'error': 'Failed to get payment status'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_supported_chains(request):
    """
    Get list of supported cryptocurrency chains
    """
    from .crypto_utils import crypto_handler

    return Response({
        'supported_chains': crypto_handler.SUPPORTED_CHAINS,
        'pricing': {
            plan: str(price) for plan, price in crypto_handler.SUBSCRIPTION_PRICING.items()
        }
    })


# Notification Views
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_notifications(request):
    """
    Get user's notifications
    """
    notifications = Notification.objects.filter(user=request.user)[:20]
    return Response({
        'notifications': NotificationSerializer(notifications, many=True).data
    })


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def mark_notification_read(request, notification_id):
    """
    Mark a notification as read
    """
    try:
        notification = Notification.objects.get(id=notification_id, user=request.user)
        if not notification.is_read:
            notification.is_read = True
            notification.read_at = timezone.now()
            notification.save()

        return Response({'success': True})
    except Notification.DoesNotExist:
        return Response({'error': 'Notification not found'}, status=status.HTTP_404_NOT_FOUND)


# User Activity and Achievements
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_user_stats(request):
    """
    Get comprehensive user statistics and achievements
    """
    from .gamification import gamification_engine

    user = request.user

    # Get comprehensive stats from gamification engine
    stats = gamification_engine.get_user_stats(user)

    # Check for new achievements
    new_achievements = gamification_engine.check_and_award_achievements(user)

    # Get all user achievements
    achievements = Achievement.objects.filter(user=user)

    return Response({
        'stats': stats,
        'achievements': AchievementSerializer(achievements, many=True).data,
        'new_achievements': AchievementSerializer(new_achievements, many=True).data if new_achievements else []
    })


# Premium Feature Access Control
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def check_premium_access(request, feature):
    """
    Check if user has access to a premium feature
    """
    try:
        subscription = Subscription.objects.get(user=request.user)
        has_access = subscription.is_active() and subscription.has_feature(feature)

        return Response({
            'has_access': has_access,
            'feature': feature,
            'subscription_plan': subscription.plan if subscription.is_active() else None,
            'expires_at': subscription.expires_at.isoformat() if subscription.is_active() else None
        })

    except Subscription.DoesNotExist:
        return Response({
            'has_access': False,
            'feature': feature,
            'message': 'No active subscription found'
        })


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def use_super_like(request):
    """
    Use a super like (premium feature)
    """
    liked_user_id = request.data.get('user_id')

    if not liked_user_id:
        return Response({'error': 'User ID required'}, status=status.HTTP_400_BAD_REQUEST)

    # Check premium access
    try:
        subscription = Subscription.objects.get(user=request.user)
        if not subscription.is_active() or not subscription.has_feature('super_likes'):
            return Response({
                'error': 'Super likes require an active premium subscription'
            }, status=status.HTTP_403_FORBIDDEN)
    except Subscription.DoesNotExist:
        return Response({
            'error': 'Premium subscription required for super likes'
        }, status=status.HTTP_403_FORBIDDEN)

    try:
        liked_user = User.objects.get(id=liked_user_id)
    except User.DoesNotExist:
        return Response({'error': 'User not found'}, status=status.HTTP_404_NOT_FOUND)

    # Check if already liked
    if Like.objects.filter(liker=request.user, liked=liked_user).exists():
        return Response({'error': 'Already liked this user'}, status=status.HTTP_400_BAD_REQUEST)

    # Create super like
    like = Like.objects.create(
        liker=request.user,
        liked=liked_user,
        is_super_like=True
    )

    # Check for mutual like (match)
    mutual_like = Like.objects.filter(liker=liked_user, liked=request.user).first()
    is_match = bool(mutual_like)
    match_id = None

    if is_match:
        match = Match.objects.create(user1=request.user, user2=liked_user)
        match_id = str(match.id)

    # Log activity and create notifications (similar to regular like)
    UserActivity.objects.create(
        user=request.user,
        activity_type='super_like_sent',
        metadata={'liked_user': str(liked_user.id)}
    )

    Notification.objects.create(
        user=liked_user,
        notification_type='super_like',
        title='Someone super liked you!',
        message=f'{request.user.name} super liked you',
        related_user=request.user
    )

    return Response({
        'success': True,
        'is_match': is_match,
        'match_id': match_id,
        'message': 'Super like sent successfully!'
    })


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def send_voice_message(request):
    """
    Send a voice message (premium feature)
    """
    match_user_id = request.data.get('user_id')

    if not match_user_id:
        return Response({'error': 'User ID required'}, status=status.HTTP_400_BAD_REQUEST)

    if 'voice_file' not in request.FILES:
        return Response({'error': 'Voice file required'}, status=status.HTTP_400_BAD_REQUEST)

    # Check premium access
    try:
        subscription = Subscription.objects.get(user=request.user)
        if not subscription.is_active() or not subscription.has_feature('voice_messages'):
            return Response({
                'error': 'Voice messages require an active premium subscription'
            }, status=status.HTTP_403_FORBIDDEN)
    except Subscription.DoesNotExist:
        return Response({
            'error': 'Premium subscription required for voice messages'
        }, status=status.HTTP_403_FORBIDDEN)

    try:
        match_user = User.objects.get(id=match_user_id)
    except User.DoesNotExist:
        return Response({'error': 'User not found'}, status=status.HTTP_404_NOT_FOUND)

    # Verify they are matched
    match = Match.objects.filter(
        Q(user1=request.user, user2=match_user) | Q(user1=match_user, user2=request.user),
        is_active=True
    ).first()

    if not match:
        return Response({'error': 'Not matched with this user'}, status=status.HTTP_403_FORBIDDEN)

    # Create voice message
    message = Message.objects.create(
        match=match,
        sender=request.user,
        receiver=match_user,
        message_type='voice',
        media_file=request.FILES['voice_file']
    )

    # Log activity and create notification
    UserActivity.objects.create(
        user=request.user,
        activity_type='message_sent',
        metadata={'message_id': str(message.id), 'receiver': str(match_user.id), 'type': 'voice'}
    )

    Notification.objects.create(
        user=match_user,
        notification_type='message',
        title='New Voice Message',
        message=f'{request.user.name} sent you a voice message',
        related_user=request.user,
        related_match=match
    )

    return Response({
        'message': MessageSerializer(message).data,
        'success': True
    })


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_premium_features(request):
    """
    Get available premium features and user's access status
    """
    features = {
        'super_likes': {
            'name': 'Super Likes',
            'description': 'Stand out with super likes that notify users immediately',
            'required_plans': ['weekly', 'fortnightly', 'monthly']
        },
        'read_receipts': {
            'name': 'Read Receipts',
            'description': 'See when your messages have been read',
            'required_plans': ['weekly', 'fortnightly', 'monthly']
        },
        'voice_messages': {
            'name': 'Voice Messages',
            'description': 'Send voice messages to your matches',
            'required_plans': ['fortnightly', 'monthly']
        },
        'video_calls': {
            'name': 'Video Calls',
            'description': 'Make video calls with your matches',
            'required_plans': ['monthly']
        },
        'priority_matching': {
            'name': 'Priority Matching',
            'description': 'Get shown to more people and see who likes you',
            'required_plans': ['monthly']
        }
    }

    # Check user's access to each feature
    user_access = {}
    try:
        subscription = Subscription.objects.get(user=request.user)
        if subscription.is_active():
            for feature_key in features.keys():
                user_access[feature_key] = subscription.has_feature(feature_key)
        else:
            user_access = {feature: False for feature in features.keys()}
    except Subscription.DoesNotExist:
        user_access = {feature: False for feature in features.keys()}

    return Response({
        'features': features,
        'user_access': user_access,
        'has_active_subscription': any(user_access.values())
    })


# Gamification Views
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_achievements(request):
    """
    Get user's achievements with detailed information
    """
    from .gamification import gamification_engine

    user = request.user
    achievements = Achievement.objects.filter(user=user).order_by('-earned_at')

    # Get available achievements
    available_achievements = []
    earned_types = set(achievements.values_list('achievement_type', flat=True))

    for achievement_type, achievement_data in gamification_engine.ACHIEVEMENTS.items():
        available_achievements.append({
            'type': achievement_type,
            'title': achievement_data['title'],
            'description': achievement_data['description'],
            'icon': achievement_data['icon'],
            'points': achievement_data['points'],
            'earned': achievement_type in earned_types
        })

    return Response({
        'earned_achievements': AchievementSerializer(achievements, many=True).data,
        'available_achievements': available_achievements,
        'total_points': sum(
            gamification_engine.ACHIEVEMENTS.get(achievement.achievement_type, {}).get('points', 0)
            for achievement in achievements
        )
    })


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_leaderboard(request):
    """
    Get achievement leaderboard
    """
    from .gamification import gamification_engine

    leaderboard = gamification_engine.get_leaderboard(limit=20)

    # Find current user's position
    user_position = None
    for i, entry in enumerate(leaderboard):
        if entry['user'].id == request.user.id:
            user_position = i + 1
            break

    leaderboard_data = []
    for i, entry in enumerate(leaderboard):
        leaderboard_data.append({
            'position': i + 1,
            'user': {
                'id': str(entry['user'].id),
                'name': entry['user'].name,
                'profile_photo': entry['user'].profile.photos.filter(is_primary=True).first().image.url if hasattr(entry['user'], 'profile') and entry['user'].profile.photos.filter(is_primary=True).exists() else None
            },
            'points': entry['points'],
            'achievements_count': entry['achievements_count']
        })

    return Response({
        'leaderboard': leaderboard_data,
        'user_position': user_position,
        'total_participants': len(leaderboard)
    })


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def trigger_achievement_check(request):
    """
    Manually trigger achievement check (for testing)
    """
    from .gamification import gamification_engine

    user = request.user
    new_achievements = gamification_engine.check_and_award_achievements(user)

    return Response({
        'new_achievements': AchievementSerializer(new_achievements, many=True).data,
        'message': f'Found {len(new_achievements)} new achievements!'
    })


# Admin Analytics Views
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_admin_analytics(request):
    """
    Get admin analytics (requires admin permissions)
    """
    if not request.user.is_staff:
        return Response({'error': 'Admin access required'}, status=status.HTTP_403_FORBIDDEN)

    from django.db.models import Count
    from django.utils import timezone
    from datetime import timedelta

    # Time periods
    today = timezone.now().date()
    week_ago = today - timedelta(days=7)
    month_ago = today - timedelta(days=30)

    # User analytics
    total_users = User.objects.count()
    new_users_week = User.objects.filter(created_at__date__gte=week_ago).count()
    new_users_month = User.objects.filter(created_at__date__gte=month_ago).count()

    # Engagement analytics
    total_likes = Like.objects.count()
    likes_week = Like.objects.filter(created_at__date__gte=week_ago).count()
    total_matches = Match.objects.filter(is_active=True).count()
    matches_week = Match.objects.filter(created_at__date__gte=week_ago).count()

    # Message analytics
    total_messages = Message.objects.count()
    messages_week = Message.objects.filter(created_at__date__gte=week_ago).count()

    # Subscription analytics
    active_subscriptions = Subscription.objects.filter(
        status='active',
        expires_at__gt=timezone.now()
    ).count()

    # Revenue analytics
    confirmed_payments = CryptoPayment.objects.filter(status='confirmed')
    total_revenue = sum(payment.amount_usd for payment in confirmed_payments)
    revenue_week = sum(
        payment.amount_usd for payment in confirmed_payments.filter(
            confirmed_at__date__gte=week_ago
        )
    )

    # Top performing users
    top_users = User.objects.annotate(
        likes_count=Count('likes_received')
    ).order_by('-likes_count')[:10]

    return Response({
        'users': {
            'total': total_users,
            'new_week': new_users_week,
            'new_month': new_users_month,
            'growth_rate_week': round((new_users_week / total_users * 100) if total_users > 0 else 0, 2)
        },
        'engagement': {
            'total_likes': total_likes,
            'likes_week': likes_week,
            'total_matches': total_matches,
            'matches_week': matches_week,
            'match_rate': round((total_matches / (total_likes / 2) * 100) if total_likes > 0 else 0, 2),
            'total_messages': total_messages,
            'messages_week': messages_week
        },
        'revenue': {
            'total_revenue': float(total_revenue),
            'revenue_week': float(revenue_week),
            'active_subscriptions': active_subscriptions,
            'conversion_rate': round((active_subscriptions / total_users * 100) if total_users > 0 else 0, 2)
        },
        'top_users': [
            {
                'id': str(user.id),
                'name': user.name,
                'email': user.email,
                'likes_received': user.likes_count,
                'created_at': user.created_at.isoformat()
            }
            for user in top_users
        ]
    })
