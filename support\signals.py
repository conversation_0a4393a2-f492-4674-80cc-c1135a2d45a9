"""
HeartGrid Support System Signals

Handle automatic notifications and updates for support system
"""

from django.db.models.signals import post_save, pre_save
from django.dispatch import receiver
from django.utils import timezone
from django.core.mail import send_mail
from django.conf import settings

from .models import SupportTicket, SupportMessage, SafetyReport, AccountVerification


@receiver(post_save, sender=SupportTicket)
def ticket_created_notification(sender, instance, created, **kwargs):
    """Send notification when ticket is created"""
    if created:
        # Send confirmation email to user
        try:
            send_mail(
                subject=f'Support Ticket Created - {instance.ticket_number}',
                message=f'''
Hello {instance.user.name},

Your support ticket has been created successfully.

Ticket Number: {instance.ticket_number}
Subject: {instance.subject}
Status: {instance.get_status_display()}

We will respond to your ticket as soon as possible.

Best regards,
HeartGrid Support Team
                ''',
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[instance.user.email],
                fail_silently=True
            )
        except Exception:
            pass  # Don't fail if email sending fails


@receiver(post_save, sender=SupportMessage)
def message_created_notification(sender, instance, created, **kwargs):
    """Send notification when message is added to ticket"""
    if created and instance.message_type == 'staff':
        # Notify user of staff response
        try:
            send_mail(
                subject=f'Response to Ticket {instance.ticket.ticket_number}',
                message=f'''
Hello {instance.ticket.user.name},

You have received a response to your support ticket.

Ticket: {instance.ticket.ticket_number} - {instance.ticket.subject}
Response: {instance.content[:200]}...

Please log in to view the full response and continue the conversation.

Best regards,
HeartGrid Support Team
                ''',
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[instance.ticket.user.email],
                fail_silently=True
            )
        except Exception:
            pass


@receiver(pre_save, sender=SupportTicket)
def ticket_status_change(sender, instance, **kwargs):
    """Handle ticket status changes"""
    if instance.pk:
        try:
            old_instance = SupportTicket.objects.get(pk=instance.pk)
            
            # Mark resolved timestamp
            if old_instance.status != 'resolved' and instance.status == 'resolved':
                instance.resolved_at = timezone.now()
            
            # Mark closed timestamp
            if old_instance.status != 'closed' and instance.status == 'closed':
                instance.closed_at = timezone.now()
                
        except SupportTicket.DoesNotExist:
            pass


@receiver(post_save, sender=SafetyReport)
def safety_report_created(sender, instance, created, **kwargs):
    """Handle safety report creation"""
    if created:
        # Send confirmation to reporter
        try:
            send_mail(
                subject='Safety Report Submitted',
                message=f'''
Hello {instance.reporter.name},

Thank you for reporting a safety concern. We take all reports seriously and will investigate promptly.

Report Type: {instance.get_report_type_display()}
Reported User: {instance.reported_user.name}

We will take appropriate action if necessary and may contact you if we need additional information.

Best regards,
HeartGrid Safety Team
                ''',
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[instance.reporter.email],
                fail_silently=True
            )
        except Exception:
            pass


@receiver(post_save, sender=AccountVerification)
def verification_status_change(sender, instance, created, **kwargs):
    """Handle verification status changes"""
    if not created and instance.status in ['approved', 'rejected']:
        # Send notification to user
        try:
            if instance.status == 'approved':
                subject = 'Account Verification Approved'
                message = f'''
Hello {instance.user.name},

Great news! Your {instance.get_verification_type_display().lower()} has been approved.

Your account now has enhanced credibility and may receive more matches.

Best regards,
HeartGrid Verification Team
                '''
            else:  # rejected
                subject = 'Account Verification Update'
                message = f'''
Hello {instance.user.name},

We were unable to approve your {instance.get_verification_type_display().lower()} at this time.

Reason: {instance.rejection_reason or 'Please ensure all requirements are met and try again.'}

You can submit a new verification request at any time.

Best regards,
HeartGrid Verification Team
                '''
            
            send_mail(
                subject=subject,
                message=message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[instance.user.email],
                fail_silently=True
            )
        except Exception:
            pass
