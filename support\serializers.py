"""
HeartGrid Support System Serializers
"""

from rest_framework import serializers
from .models import SupportTicket, SupportMessage, FAQ, SafetyReport, AccountVerification


class SupportMessageSerializer(serializers.ModelSerializer):
    sender_name = serializers.CharField(source='sender.name', read_only=True)
    
    class Meta:
        model = SupportMessage
        fields = [
            'id', 'message_type', 'content', 'created_at', 
            'sender_name', 'is_internal'
        ]


class SupportTicketSerializer(serializers.ModelSerializer):
    category_name = serializers.CharField(source='category.name', read_only=True)
    messages = SupportMessageSerializer(many=True, read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    priority_display = serializers.CharField(source='get_priority_display', read_only=True)
    
    class Meta:
        model = SupportTicket
        fields = [
            'id', 'ticket_number', 'subject', 'description', 'status', 
            'status_display', 'priority', 'priority_display', 'category_name',
            'created_at', 'updated_at', 'messages', 'satisfaction_rating'
        ]


class FAQSerializer(serializers.ModelSerializer):
    category_display = serializers.CharField(source='get_category_display', read_only=True)
    
    class Meta:
        model = FAQ
        fields = [
            'id', 'category', 'category_display', 'question', 'answer',
            'view_count', 'helpful_count'
        ]


class SafetyReportSerializer(serializers.ModelSerializer):
    reported_user_name = serializers.CharField(source='reported_user.name', read_only=True)
    report_type_display = serializers.CharField(source='get_report_type_display', read_only=True)
    
    class Meta:
        model = SafetyReport
        fields = [
            'id', 'reported_user', 'reported_user_name', 'report_type', 
            'report_type_display', 'description', 'status', 'created_at'
        ]
        read_only_fields = ['id', 'status', 'created_at']


class AccountVerificationSerializer(serializers.ModelSerializer):
    verification_type_display = serializers.CharField(source='get_verification_type_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    
    class Meta:
        model = AccountVerification
        fields = [
            'id', 'verification_type', 'verification_type_display', 
            'status', 'status_display', 'created_at', 'reviewed_at',
            'rejection_reason'
        ]
