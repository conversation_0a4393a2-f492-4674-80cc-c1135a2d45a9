"""
Django signals for HeartGrid Communications

This module contains signal handlers for automatic model creation and updates.
"""

from django.db.models.signals import post_save
from django.dispatch import receiver
from django.contrib.auth import get_user_model
from .models import UserPresence

User = get_user_model()


@receiver(post_save, sender=User)
def create_user_presence(sender, instance, created, **kwargs):
    """
    Create UserPresence instance when a new user is created
    """
    if created:
        UserPresence.objects.create(user=instance)


@receiver(post_save, sender=User)
def save_user_presence(sender, instance, **kwargs):
    """
    Ensure UserPresence exists for user
    """
    if not hasattr(instance, 'presence'):
        UserPresence.objects.create(user=instance)
