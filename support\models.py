"""
HeartGrid Support System Models

Comprehensive support system for dating platform including:
- Ticket management
- Live chat support
- FAQ system
- Safety reporting
- Account verification
"""

import uuid
from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.core.validators import MinValueValidator, MaxValueValidator

User = get_user_model()


class SupportCategory(models.Model):
    """Categories for support tickets"""
    CATEGORY_TYPES = [
        ('account', 'Account Issues'),
        ('billing', 'Billing & Subscriptions'),
        ('safety', 'Safety & Security'),
        ('technical', 'Technical Issues'),
        ('profile', 'Profile & Photos'),
        ('matching', 'Matching & Discovery'),
        ('messaging', 'Messaging & Chat'),
        ('verification', 'Account Verification'),
        ('harassment', 'Harassment Report'),
        ('fake_profile', 'Fake Profile Report'),
        ('inappropriate', 'Inappropriate Content'),
        ('other', 'Other'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=100)
    category_type = models.CharField(max_length=20, choices=CATEGORY_TYPES)
    description = models.TextField()
    is_active = models.BooleanField(default=True)
    priority_level = models.IntegerField(default=3, validators=[MinValueValidator(1), MaxValueValidator(5)])
    auto_response_template = models.TextField(blank=True, help_text="Auto-response message template")
    
    class Meta:
        db_table = 'support_categories'
        ordering = ['priority_level', 'name']
    
    def __str__(self):
        return self.name


class SupportTicket(models.Model):
    """Support tickets for user issues"""
    STATUS_CHOICES = [
        ('open', 'Open'),
        ('in_progress', 'In Progress'),
        ('waiting_user', 'Waiting for User'),
        ('resolved', 'Resolved'),
        ('closed', 'Closed'),
        ('escalated', 'Escalated'),
    ]
    
    PRIORITY_CHOICES = [
        (1, 'Critical'),
        (2, 'High'),
        (3, 'Medium'),
        (4, 'Low'),
        (5, 'Lowest'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    ticket_number = models.CharField(max_length=20, unique=True)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='support_tickets')
    category = models.ForeignKey(SupportCategory, on_delete=models.SET_NULL, null=True)
    
    subject = models.CharField(max_length=200)
    description = models.TextField()
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='open')
    priority = models.IntegerField(choices=PRIORITY_CHOICES, default=3)
    
    # Assignment
    assigned_to = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, 
                                   related_name='assigned_tickets')
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    resolved_at = models.DateTimeField(null=True, blank=True)
    closed_at = models.DateTimeField(null=True, blank=True)
    
    # Metadata
    user_agent = models.TextField(blank=True)
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    attachments_count = models.IntegerField(default=0)
    
    # Satisfaction
    satisfaction_rating = models.IntegerField(null=True, blank=True, 
                                            validators=[MinValueValidator(1), MaxValueValidator(5)])
    satisfaction_feedback = models.TextField(blank=True)
    
    class Meta:
        db_table = 'support_tickets'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['status', 'priority']),
            models.Index(fields=['user', 'status']),
            models.Index(fields=['assigned_to', 'status']),
        ]
    
    def save(self, *args, **kwargs):
        if not self.ticket_number:
            self.ticket_number = self.generate_ticket_number()
        super().save(*args, **kwargs)
    
    def generate_ticket_number(self):
        """Generate unique ticket number"""
        import random
        import string
        while True:
            number = 'HG' + ''.join(random.choices(string.digits, k=8))
            if not SupportTicket.objects.filter(ticket_number=number).exists():
                return number
    
    def __str__(self):
        return f"{self.ticket_number} - {self.subject}"


class SupportMessage(models.Model):
    """Messages within support tickets"""
    MESSAGE_TYPES = [
        ('user', 'User Message'),
        ('staff', 'Staff Response'),
        ('system', 'System Message'),
        ('auto', 'Auto Response'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    ticket = models.ForeignKey(SupportTicket, on_delete=models.CASCADE, related_name='messages')
    sender = models.ForeignKey(User, on_delete=models.CASCADE)
    message_type = models.CharField(max_length=10, choices=MESSAGE_TYPES)
    
    content = models.TextField()
    is_internal = models.BooleanField(default=False, help_text="Internal staff notes")
    
    created_at = models.DateTimeField(auto_now_add=True)
    read_by_user = models.BooleanField(default=False)
    read_by_staff = models.BooleanField(default=False)
    
    class Meta:
        db_table = 'support_messages'
        ordering = ['created_at']
    
    def __str__(self):
        return f"Message in {self.ticket.ticket_number} by {self.sender.name}"


class SupportAttachment(models.Model):
    """File attachments for support tickets"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    ticket = models.ForeignKey(SupportTicket, on_delete=models.CASCADE, related_name='attachments')
    message = models.ForeignKey(SupportMessage, on_delete=models.CASCADE, null=True, blank=True)
    
    file = models.FileField(upload_to='support_attachments/%Y/%m/')
    original_filename = models.CharField(max_length=255)
    file_size = models.PositiveIntegerField()
    content_type = models.CharField(max_length=100)
    
    uploaded_by = models.ForeignKey(User, on_delete=models.CASCADE)
    uploaded_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'support_attachments'
    
    def __str__(self):
        return f"{self.original_filename} - {self.ticket.ticket_number}"


class FAQ(models.Model):
    """Frequently Asked Questions"""
    CATEGORIES = [
        ('getting_started', 'Getting Started'),
        ('profile', 'Profile & Photos'),
        ('matching', 'Matching & Discovery'),
        ('messaging', 'Messaging'),
        ('safety', 'Safety & Security'),
        ('billing', 'Billing & Subscriptions'),
        ('technical', 'Technical Issues'),
        ('account', 'Account Management'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    category = models.CharField(max_length=20, choices=CATEGORIES)
    question = models.CharField(max_length=300)
    answer = models.TextField()
    
    is_published = models.BooleanField(default=True)
    order = models.IntegerField(default=0)
    view_count = models.PositiveIntegerField(default=0)
    helpful_count = models.PositiveIntegerField(default=0)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'support_faq'
        ordering = ['category', 'order', 'question']
    
    def __str__(self):
        return self.question


class SafetyReport(models.Model):
    """Safety reports for inappropriate behavior"""
    REPORT_TYPES = [
        ('harassment', 'Harassment'),
        ('inappropriate_photos', 'Inappropriate Photos'),
        ('fake_profile', 'Fake Profile'),
        ('spam', 'Spam'),
        ('underage', 'Underage User'),
        ('scam', 'Scam/Fraud'),
        ('violence', 'Threats of Violence'),
        ('hate_speech', 'Hate Speech'),
        ('other', 'Other'),
    ]
    
    STATUS_CHOICES = [
        ('pending', 'Pending Review'),
        ('investigating', 'Under Investigation'),
        ('action_taken', 'Action Taken'),
        ('no_action', 'No Action Required'),
        ('dismissed', 'Dismissed'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    reporter = models.ForeignKey(User, on_delete=models.CASCADE, related_name='safety_reports_made')
    reported_user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='safety_reports_received')
    
    report_type = models.CharField(max_length=20, choices=REPORT_TYPES)
    description = models.TextField()
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    
    # Evidence
    screenshot_urls = models.JSONField(default=list, blank=True)
    conversation_id = models.UUIDField(null=True, blank=True)
    
    # Review
    reviewed_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True,
                                   related_name='safety_reports_reviewed')
    review_notes = models.TextField(blank=True)
    action_taken = models.TextField(blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    reviewed_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        db_table = 'support_safety_reports'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['status', 'report_type']),
            models.Index(fields=['reported_user', 'status']),
        ]
    
    def __str__(self):
        return f"Safety Report: {self.report_type} - {self.reported_user.name}"


class LiveChatSession(models.Model):
    """Live chat support sessions"""
    STATUS_CHOICES = [
        ('waiting', 'Waiting for Agent'),
        ('active', 'Active Chat'),
        ('ended', 'Chat Ended'),
        ('abandoned', 'User Abandoned'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='chat_sessions')
    agent = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True,
                             related_name='agent_chat_sessions')

    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='waiting')
    category = models.ForeignKey(SupportCategory, on_delete=models.SET_NULL, null=True)

    started_at = models.DateTimeField(auto_now_add=True)
    agent_joined_at = models.DateTimeField(null=True, blank=True)
    ended_at = models.DateTimeField(null=True, blank=True)

    # Satisfaction
    rating = models.IntegerField(null=True, blank=True,
                               validators=[MinValueValidator(1), MaxValueValidator(5)])
    feedback = models.TextField(blank=True)

    class Meta:
        db_table = 'support_live_chat_sessions'
        ordering = ['-started_at']

    def __str__(self):
        return f"Chat Session {self.id} - {self.user.name}"


class AccountVerification(models.Model):
    """Account verification requests"""
    VERIFICATION_TYPES = [
        ('photo', 'Photo Verification'),
        ('phone', 'Phone Verification'),
        ('identity', 'Identity Verification'),
        ('social', 'Social Media Verification'),
    ]

    STATUS_CHOICES = [
        ('pending', 'Pending Review'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
        ('needs_more_info', 'Needs More Information'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='verification_requests')
    verification_type = models.CharField(max_length=20, choices=VERIFICATION_TYPES)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')

    # Verification data
    verification_data = models.JSONField(default=dict)  # Store verification details
    submitted_files = models.JSONField(default=list)    # File paths/URLs

    # Review
    reviewed_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True,
                                   related_name='verifications_reviewed')
    review_notes = models.TextField(blank=True)
    rejection_reason = models.TextField(blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    reviewed_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        db_table = 'support_account_verification'
        ordering = ['-created_at']
        unique_together = ['user', 'verification_type']

    def __str__(self):
        return f"{self.verification_type.title()} Verification - {self.user.name}"
