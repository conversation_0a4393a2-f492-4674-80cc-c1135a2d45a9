"""
WebRTC Configuration for HeartGrid Communications
Handles STUN/TURN server configuration for production deployments
"""

import os
from django.conf import settings


class WebRTCConfig:
    """
    WebRTC configuration manager for ICE servers
    """
    
    @staticmethod
    def get_ice_servers():
        """
        Get ICE server configuration for WebRTC
        Returns a list of ICE servers including STUN and TURN servers
        """
        ice_servers = []
        
        # Add STUN servers (always available)
        ice_servers.extend(WebRTCConfig.get_stun_servers())
        
        # Add TURN servers if configured
        turn_servers = WebRTCConfig.get_turn_servers()
        if turn_servers:
            ice_servers.extend(turn_servers)
        
        return ice_servers
    
    @staticmethod
    def get_stun_servers():
        """
        Get STUN server configuration
        """
        # Default public STUN servers
        default_stun_servers = [
            {'urls': 'stun:stun.l.google.com:19302'},
            {'urls': 'stun:stun1.l.google.com:19302'},
            {'urls': 'stun:stun2.l.google.com:19302'},
            {'urls': 'stun:stun3.l.google.com:19302'},
            {'urls': 'stun:stun4.l.google.com:19302'},
            {'urls': 'stun:stun.stunprotocol.org:3478'},
            {'urls': 'stun:stun.voiparound.com'},
            {'urls': 'stun:stun.voipbuster.com'},
            {'urls': 'stun:stun.voipstunt.com'},
            {'urls': 'stun:stun.voxgratia.org'}
        ]
        
        # Allow custom STUN servers from settings
        custom_stun = getattr(settings, 'WEBRTC_STUN_SERVERS', None)
        if custom_stun:
            return custom_stun
        
        return default_stun_servers
    
    @staticmethod
    def get_turn_servers():
        """
        Get TURN server configuration from environment variables or settings
        """
        turn_servers = []
        
        # Check Django settings first
        if hasattr(settings, 'WEBRTC_TURN_SERVERS'):
            return settings.WEBRTC_TURN_SERVERS
        
        # Check environment variables
        turn_url = os.getenv('TURN_SERVER_URL')
        turn_username = os.getenv('TURN_USERNAME')
        turn_credential = os.getenv('TURN_CREDENTIAL')
        
        if turn_url and turn_username and turn_credential:
            turn_servers.append({
                'urls': turn_url,
                'username': turn_username,
                'credential': turn_credential,
                'credentialType': 'password'
            })
        
        # Support multiple TURN servers
        turn_urls = os.getenv('TURN_SERVER_URLS', '').split(',')
        turn_usernames = os.getenv('TURN_USERNAMES', '').split(',')
        turn_credentials = os.getenv('TURN_CREDENTIALS', '').split(',')
        
        if len(turn_urls) == len(turn_usernames) == len(turn_credentials) and turn_urls[0]:
            for url, username, credential in zip(turn_urls, turn_usernames, turn_credentials):
                if url.strip() and username.strip() and credential.strip():
                    turn_servers.append({
                        'urls': url.strip(),
                        'username': username.strip(),
                        'credential': credential.strip(),
                        'credentialType': 'password'
                    })
        
        return turn_servers
    
    @staticmethod
    def get_webrtc_configuration():
        """
        Get complete WebRTC configuration including ICE servers and other options
        """
        return {
            'iceServers': WebRTCConfig.get_ice_servers(),
            'iceCandidatePoolSize': getattr(settings, 'WEBRTC_ICE_CANDIDATE_POOL_SIZE', 10),
            'bundlePolicy': getattr(settings, 'WEBRTC_BUNDLE_POLICY', 'max-bundle'),
            'rtcpMuxPolicy': getattr(settings, 'WEBRTC_RTCP_MUX_POLICY', 'require'),
            'iceTransportPolicy': getattr(settings, 'WEBRTC_ICE_TRANSPORT_POLICY', 'all')
        }
    
    @staticmethod
    def get_media_constraints():
        """
        Get media constraints for getUserMedia
        """
        return {
            'audio': getattr(settings, 'WEBRTC_AUDIO_CONSTRAINTS', {
                'echoCancellation': True,
                'noiseSuppression': True,
                'autoGainControl': True,
                'sampleRate': 44100
            }),
            'video': getattr(settings, 'WEBRTC_VIDEO_CONSTRAINTS', {
                'width': {'min': 320, 'ideal': 1280, 'max': 1920},
                'height': {'min': 240, 'ideal': 720, 'max': 1080},
                'frameRate': {'min': 15, 'ideal': 30, 'max': 60}
            })
        }
    
    @staticmethod
    def is_turn_required():
        """
        Check if TURN servers are configured and required
        """
        return len(WebRTCConfig.get_turn_servers()) > 0
    
    @staticmethod
    def validate_configuration():
        """
        Validate WebRTC configuration
        Returns tuple (is_valid, errors)
        """
        errors = []
        
        # Check if at least STUN servers are available
        stun_servers = WebRTCConfig.get_stun_servers()
        if not stun_servers:
            errors.append("No STUN servers configured")
        
        # Validate TURN server configuration if present
        turn_servers = WebRTCConfig.get_turn_servers()
        for i, server in enumerate(turn_servers):
            if not server.get('urls'):
                errors.append(f"TURN server {i+1}: Missing URLs")
            if not server.get('username'):
                errors.append(f"TURN server {i+1}: Missing username")
            if not server.get('credential'):
                errors.append(f"TURN server {i+1}: Missing credential")
        
        return len(errors) == 0, errors


# Production TURN server recommendations
RECOMMENDED_TURN_PROVIDERS = {
    'twilio': {
        'name': 'Twilio STUN/TURN',
        'description': 'Reliable commercial TURN service with global infrastructure',
        'url': 'https://www.twilio.com/stun-turn'
    },
    'xirsys': {
        'name': 'Xirsys',
        'description': 'WebRTC infrastructure provider with TURN services',
        'url': 'https://xirsys.com/'
    },
    'metered': {
        'name': 'Metered TURN',
        'description': 'Global TURN server network with pay-as-you-go pricing',
        'url': 'https://www.metered.ca/tools/openrelay/'
    },
    'coturn': {
        'name': 'Coturn (Self-hosted)',
        'description': 'Open source TURN server for self-hosting',
        'url': 'https://github.com/coturn/coturn'
    }
}


def get_webrtc_context():
    """
    Get WebRTC configuration context for templates
    """
    config = WebRTCConfig.get_webrtc_configuration()
    is_valid, errors = WebRTCConfig.validate_configuration()
    
    return {
        'webrtc_config': config,
        'webrtc_valid': is_valid,
        'webrtc_errors': errors,
        'turn_configured': WebRTCConfig.is_turn_required(),
        'media_constraints': WebRTCConfig.get_media_constraints()
    }
